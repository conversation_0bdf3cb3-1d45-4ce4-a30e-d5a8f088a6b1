﻿Imports System.Text.RegularExpressions
Module OthmanCode_Module
    Public CurrentUserName As String = ""
    Public CurrentPassWord As String = ""
    Public CurrenttxtLicKey As String = ""
    Public CurrentxtSystemKey As String = ""
    Public Function UpperCase_FirstChar(ByVal Word As String) As String
        Dim s As String = Word.Trim
        Dim s2 As String = StrConv(s, VbStrConv.ProperCase)
        Return s2
    End Function
    Public Function ExtractCompanyName(ByVal Word As String) As String
        Dim arr = Split(Word, "@")
        Dim compname As String = arr(1).Substring(0, arr(1).IndexOf("."))
        Dim s As String = compname.Trim
        Dim s2 As String = StrConv(s, VbStrConv.ProperCase)
        Return s2
    End Function
    Public Function ReplaceLink(ByVal OriginalString As String, ByVal NewLink As String, Optional ByVal FuncToChange As String = "", Optional ByVal NewName As String = "") As String
        Dim urlStr As String
        urlStr = OriginalString
        Dim MyRegex As Regex = New Regex("(?:(http|https|ftp)://|www\.)[a-zA-Z0-9.-]+(\.[a-zA-Z]{2,3})?(:[a-zA-Z0-9]*)?/?([a-zA-Z0-9._?,'/\\+&%$#=~-])*")
        Dim url As String = ""
        Dim value As String = NewLink
        If Not value.StartsWith("https://") Then
            url = "https://" & NewLink
        End If
        Dim xxx = (MyRegex.Replace(urlStr, url))
        If FuncToChange <> String.Empty And NewName <> String.Empty Then
            Dim Link_ As String = "<a href=" & url & ">" & NewName & "</a>"
            xxx = xxx.Replace(FuncToChange, Link_)
        End If
        'Dim address As Uri = New Uri(url)
        'MsgBox("http://" & address.Host)
        Return xxx
    End Function
    Public Function ReplaceLogo(ByVal OriginalString As String, ByVal LogoString As String, Optional ByVal FuncToChange As String = "", Optional ByVal NewName As String = "") As String
        Dim letterString As String = OriginalString
        Dim xxx = "<p><img src=cid:MyPic width=64px height=64px></p>"
        letterString = letterString.Replace("[-Logo-]", xxx)
        Return letterString
    End Function
End Module
