﻿Imports System.Net.Http
Imports System.Threading.Tasks
Imports DevExpress.XtraEditors
Public Class SuspiciousWordsLoader
    Public Shared Async Function LoadSuspiciousWordsFromTextBin(url As String) As Task(Of HashSet(Of String))
        Dim words As New HashSet(Of String)(StringComparer.OrdinalIgnoreCase)
        Try
            Using client As New HttpClient()
                ' ضبط User-Agent لمحاكاة متصفح حقيقي
                client.DefaultRequestHeaders.UserAgent.ParseAdd("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36")
                Dim response As HttpResponseMessage = Await client.GetAsync(url).ConfigureAwait(False)
                If response.IsSuccessStatusCode Then
                    Dim content As String = Await response.Content.ReadAsStringAsync().ConfigureAwait(False)
                    Dim lines As String() = content.Split(New String() {vbCrLf, vbLf}, StringSplitOptions.RemoveEmptyEntries)
                    For Each line In lines
                        words.Add(line.Trim())
                    Next
                Else
                    XtraMessageBox.Show(" Error while loading  words: " & response.StatusCode.ToString())
                End If
            End Using
        Catch ex As Exception
            XtraMessageBox.Show("Error while loading suspicious words: " & ex.Message)
        End Try
        Return words
    End Function
End Class
