﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class frmAddToAllAccounts
    Inherits DevExpress.XtraEditors.XtraForm
    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub
    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer
    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmAddToAllAccounts))
        Me.DxErrorProvider1 = New DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider(Me.components)
        Me.LayoutControl1 = New DevExpress.XtraLayout.LayoutControl()
        Me.TxtFromMail = New DevExpress.XtraEditors.TextEdit()
        Me.BntNew = New DevExpress.XtraEditors.SimpleButton()
        Me.BntAdd = New DevExpress.XtraEditors.SimpleButton()
        Me.TxtSubject = New DevExpress.XtraEditors.TextEdit()
        Me.TxtTag_2 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.TxtFromName = New DevExpress.XtraEditors.TextEdit()
        Me.TxtTag_1 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Root = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem1 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.EmptySpaceItem1 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.LayoutControlItem2 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem3 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem4 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem5 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem6 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem7 = New DevExpress.XtraLayout.LayoutControlItem()
        CType(Me.DxErrorProvider1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.LayoutControl1.SuspendLayout()
        CType(Me.TxtFromMail.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TxtSubject.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TxtTag_2.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TxtFromName.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TxtTag_1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Root, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem6, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem7, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'DxErrorProvider1
        '
        Me.DxErrorProvider1.ContainerControl = Me
        '
        'LayoutControl1
        '
        Me.LayoutControl1.Controls.Add(Me.TxtFromMail)
        Me.LayoutControl1.Controls.Add(Me.BntNew)
        Me.LayoutControl1.Controls.Add(Me.BntAdd)
        Me.LayoutControl1.Controls.Add(Me.TxtSubject)
        Me.LayoutControl1.Controls.Add(Me.TxtTag_2)
        Me.LayoutControl1.Controls.Add(Me.TxtFromName)
        Me.LayoutControl1.Controls.Add(Me.TxtTag_1)
        Me.LayoutControl1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.LayoutControl1.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControl1.Name = "LayoutControl1"
        Me.LayoutControl1.Root = Me.Root
        Me.LayoutControl1.Size = New System.Drawing.Size(523, 318)
        Me.LayoutControl1.TabIndex = 0
        Me.LayoutControl1.Text = "LayoutControl1"
        '
        'TxtFromMail
        '
        Me.TxtFromMail.EditValue = ""
        Me.TxtFromMail.Location = New System.Drawing.Point(18, 269)
        Me.TxtFromMail.Margin = New System.Windows.Forms.Padding(5, 3, 5, 3)
        Me.TxtFromMail.Name = "TxtFromMail"
        Me.TxtFromMail.Properties.Appearance.BackColor = System.Drawing.Color.White
        Me.TxtFromMail.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TxtFromMail.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.TxtFromMail.Properties.Appearance.Options.UseBackColor = True
        Me.TxtFromMail.Properties.Appearance.Options.UseFont = True
        Me.TxtFromMail.Properties.Appearance.Options.UseForeColor = True
        Me.TxtFromMail.Properties.NullValuePrompt = "Enter from name enail title..."
        Me.TxtFromMail.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TxtFromMail.Size = New System.Drawing.Size(487, 34)
        Me.TxtFromMail.StyleController = Me.LayoutControl1
        Me.TxtFromMail.TabIndex = 395
        Me.TxtFromMail.Visible = False
        '
        'BntNew
        '
        Me.BntNew.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntNew.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntNew.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntNew.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntNew.Appearance.Options.UseBackColor = True
        Me.BntNew.Appearance.Options.UseBorderColor = True
        Me.BntNew.Appearance.Options.UseFont = True
        Me.BntNew.Appearance.Options.UseForeColor = True
        Me.BntNew.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntNew.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntNew.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntNew.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntNew.AppearanceDisabled.Options.UseBackColor = True
        Me.BntNew.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntNew.AppearanceDisabled.Options.UseFont = True
        Me.BntNew.AppearanceDisabled.Options.UseForeColor = True
        Me.BntNew.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntNew.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntNew.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntNew.AppearanceHovered.Options.UseBackColor = True
        Me.BntNew.AppearanceHovered.Options.UseBorderColor = True
        Me.BntNew.AppearanceHovered.Options.UseForeColor = True
        Me.BntNew.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntNew.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntNew.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntNew.AppearancePressed.Options.UseBackColor = True
        Me.BntNew.AppearancePressed.Options.UseBorderColor = True
        Me.BntNew.AppearancePressed.Options.UseForeColor = True
        Me.BntNew.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_32x322
        Me.BntNew.Location = New System.Drawing.Point(121, 211)
        Me.BntNew.Name = "BntNew"
        Me.BntNew.Size = New System.Drawing.Size(159, 38)
        Me.BntNew.StyleController = Me.LayoutControl1
        Me.BntNew.TabIndex = 394
        Me.BntNew.Text = "Clear All"
        '
        'BntAdd
        '
        Me.BntAdd.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntAdd.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntAdd.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntAdd.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntAdd.Appearance.Options.UseBackColor = True
        Me.BntAdd.Appearance.Options.UseBorderColor = True
        Me.BntAdd.Appearance.Options.UseFont = True
        Me.BntAdd.Appearance.Options.UseForeColor = True
        Me.BntAdd.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntAdd.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntAdd.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntAdd.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntAdd.AppearanceDisabled.Options.UseBackColor = True
        Me.BntAdd.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntAdd.AppearanceDisabled.Options.UseFont = True
        Me.BntAdd.AppearanceDisabled.Options.UseForeColor = True
        Me.BntAdd.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntAdd.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntAdd.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntAdd.AppearanceHovered.Options.UseBackColor = True
        Me.BntAdd.AppearanceHovered.Options.UseBorderColor = True
        Me.BntAdd.AppearanceHovered.Options.UseForeColor = True
        Me.BntAdd.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntAdd.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntAdd.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntAdd.AppearancePressed.Options.UseBackColor = True
        Me.BntAdd.AppearancePressed.Options.UseBorderColor = True
        Me.BntAdd.AppearancePressed.Options.UseForeColor = True
        Me.BntAdd.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Transfer32x32
        Me.BntAdd.Location = New System.Drawing.Point(286, 211)
        Me.BntAdd.Name = "BntAdd"
        Me.BntAdd.Size = New System.Drawing.Size(173, 38)
        Me.BntAdd.StyleController = Me.LayoutControl1
        Me.BntAdd.TabIndex = 393
        Me.BntAdd.Text = "Update"
        '
        'TxtSubject
        '
        Me.TxtSubject.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.TxtSubject.EditValue = ""
        Me.TxtSubject.Location = New System.Drawing.Point(115, 162)
        Me.TxtSubject.Margin = New System.Windows.Forms.Padding(5, 3, 5, 3)
        Me.TxtSubject.Name = "TxtSubject"
        Me.TxtSubject.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.TxtSubject.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.TxtSubject.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.TxtSubject.Properties.Appearance.Options.UseBackColor = True
        Me.TxtSubject.Properties.Appearance.Options.UseFont = True
        Me.TxtSubject.Properties.Appearance.Options.UseForeColor = True
        Me.TxtSubject.Properties.NullValuePrompt = "Subject"
        Me.TxtSubject.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TxtSubject.Size = New System.Drawing.Size(344, 34)
        Me.TxtSubject.StyleController = Me.LayoutControl1
        Me.TxtSubject.TabIndex = 392
        '
        'TxtTag_2
        '
        Me.TxtTag_2.Cursor = System.Windows.Forms.Cursors.Hand
        Me.TxtTag_2.EditValue = ""
        Me.TxtTag_2.Location = New System.Drawing.Point(115, 113)
        Me.TxtTag_2.Margin = New System.Windows.Forms.Padding(5, 3, 5, 3)
        Me.TxtTag_2.Name = "TxtTag_2"
        Me.TxtTag_2.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.TxtTag_2.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.TxtTag_2.Properties.Appearance.Options.UseBackColor = True
        Me.TxtTag_2.Properties.Appearance.Options.UseFont = True
        Me.TxtTag_2.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.TxtTag_2.Properties.Items.AddRange(New Object() {"Random Character 3", "Random Character 4", "Random Character 5", "Random Character 6", "Random Character 7", "Random Country", "Random IP", "Random IP China", "Random Number 3", "Random Number 4", "Random Number 5", "Random Number 6", "Random Number 7", "Receiver Email", "Show Company Name", "Show Date", "Show Date Tomorrow", "Show Name", "Show Random Fake Phone Numbers", "Show The Domain", "Show Time", "Start Name With Upper Case"})
        Me.TxtTag_2.Size = New System.Drawing.Size(344, 34)
        Me.TxtTag_2.StyleController = Me.LayoutControl1
        Me.TxtTag_2.TabIndex = 391
        '
        'TxtFromName
        '
        Me.TxtFromName.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.TxtFromName.EditValue = ""
        Me.TxtFromName.Location = New System.Drawing.Point(115, 64)
        Me.TxtFromName.Margin = New System.Windows.Forms.Padding(5, 3, 5, 3)
        Me.TxtFromName.Name = "TxtFromName"
        Me.TxtFromName.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.TxtFromName.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.TxtFromName.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.TxtFromName.Properties.Appearance.Options.UseBackColor = True
        Me.TxtFromName.Properties.Appearance.Options.UseFont = True
        Me.TxtFromName.Properties.Appearance.Options.UseForeColor = True
        Me.TxtFromName.Properties.NullValuePrompt = "From Name"
        Me.TxtFromName.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TxtFromName.Size = New System.Drawing.Size(344, 34)
        Me.TxtFromName.StyleController = Me.LayoutControl1
        Me.TxtFromName.TabIndex = 390
        '
        'TxtTag_1
        '
        Me.TxtTag_1.Cursor = System.Windows.Forms.Cursors.Hand
        Me.TxtTag_1.EditValue = ""
        Me.TxtTag_1.Location = New System.Drawing.Point(115, 15)
        Me.TxtTag_1.Margin = New System.Windows.Forms.Padding(5, 3, 5, 3)
        Me.TxtTag_1.Name = "TxtTag_1"
        Me.TxtTag_1.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.TxtTag_1.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.TxtTag_1.Properties.Appearance.Options.UseBackColor = True
        Me.TxtTag_1.Properties.Appearance.Options.UseFont = True
        Me.TxtTag_1.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.TxtTag_1.Properties.Items.AddRange(New Object() {"Random Character 3", "Random Character 4", "Random Character 5", "Random Character 6", "Random Character 7", "Random Country", "Random IP", "Random IP China", "Random Number 3", "Random Number 4", "Random Number 5", "Random Number 6", "Random Number 7", "Receiver Email", "Show Company Name", "Show Date", "Show Date Tomorrow", "Show Name", "Show Random Fake Phone Numbers", "Show The Domain", "Show Time", "Start Name With Upper Case"})
        Me.TxtTag_1.Size = New System.Drawing.Size(344, 34)
        Me.TxtTag_1.StyleController = Me.LayoutControl1
        Me.TxtTag_1.TabIndex = 389
        '
        'Root
        '
        Me.Root.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.Root.GroupBordersVisible = False
        Me.Root.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem1, Me.EmptySpaceItem1, Me.LayoutControlItem2, Me.LayoutControlItem3, Me.LayoutControlItem4, Me.LayoutControlItem5, Me.LayoutControlItem6, Me.LayoutControlItem7})
        Me.Root.Name = "Root"
        Me.Root.Size = New System.Drawing.Size(523, 318)
        Me.Root.TextVisible = False
        '
        'LayoutControlItem1
        '
        Me.LayoutControlItem1.Control = Me.TxtTag_1
        Me.LayoutControlItem1.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem1.Name = "LayoutControlItem1"
        Me.LayoutControlItem1.Size = New System.Drawing.Size(493, 49)
        Me.LayoutControlItem1.Spacing = New DevExpress.XtraLayout.Utils.Padding(0, 46, 0, 9)
        Me.LayoutControlItem1.Text = "Name Tag :"
        Me.LayoutControlItem1.TextSize = New System.Drawing.Size(79, 16)
        '
        'EmptySpaceItem1
        '
        Me.EmptySpaceItem1.Location = New System.Drawing.Point(0, 240)
        Me.EmptySpaceItem1.Name = "EmptySpaceItem1"
        Me.EmptySpaceItem1.Size = New System.Drawing.Size(493, 14)
        '
        'LayoutControlItem2
        '
        Me.LayoutControlItem2.Control = Me.TxtFromName
        Me.LayoutControlItem2.Location = New System.Drawing.Point(0, 49)
        Me.LayoutControlItem2.Name = "LayoutControlItem2"
        Me.LayoutControlItem2.Size = New System.Drawing.Size(493, 49)
        Me.LayoutControlItem2.Spacing = New DevExpress.XtraLayout.Utils.Padding(0, 46, 0, 9)
        Me.LayoutControlItem2.Text = "From Name :"
        Me.LayoutControlItem2.TextSize = New System.Drawing.Size(79, 16)
        '
        'LayoutControlItem3
        '
        Me.LayoutControlItem3.Control = Me.TxtTag_2
        Me.LayoutControlItem3.Location = New System.Drawing.Point(0, 98)
        Me.LayoutControlItem3.Name = "LayoutControlItem3"
        Me.LayoutControlItem3.Size = New System.Drawing.Size(493, 49)
        Me.LayoutControlItem3.Spacing = New DevExpress.XtraLayout.Utils.Padding(0, 46, 0, 9)
        Me.LayoutControlItem3.Text = "Subject Tag :"
        Me.LayoutControlItem3.TextSize = New System.Drawing.Size(79, 16)
        '
        'LayoutControlItem4
        '
        Me.LayoutControlItem4.Control = Me.TxtSubject
        Me.LayoutControlItem4.Location = New System.Drawing.Point(0, 147)
        Me.LayoutControlItem4.Name = "LayoutControlItem4"
        Me.LayoutControlItem4.Size = New System.Drawing.Size(493, 49)
        Me.LayoutControlItem4.Spacing = New DevExpress.XtraLayout.Utils.Padding(0, 46, 0, 9)
        Me.LayoutControlItem4.Text = "Subject :"
        Me.LayoutControlItem4.TextSize = New System.Drawing.Size(79, 16)
        '
        'LayoutControlItem5
        '
        Me.LayoutControlItem5.Control = Me.BntAdd
        Me.LayoutControlItem5.Location = New System.Drawing.Point(268, 196)
        Me.LayoutControlItem5.Name = "LayoutControlItem5"
        Me.LayoutControlItem5.Size = New System.Drawing.Size(225, 44)
        Me.LayoutControlItem5.Spacing = New DevExpress.XtraLayout.Utils.Padding(0, 46, 0, 0)
        Me.LayoutControlItem5.TextVisible = False
        '
        'LayoutControlItem6
        '
        Me.LayoutControlItem6.Control = Me.BntNew
        Me.LayoutControlItem6.Location = New System.Drawing.Point(0, 196)
        Me.LayoutControlItem6.Name = "LayoutControlItem6"
        Me.LayoutControlItem6.Size = New System.Drawing.Size(268, 44)
        Me.LayoutControlItem6.Spacing = New DevExpress.XtraLayout.Utils.Padding(103, 0, 0, 0)
        Me.LayoutControlItem6.TextVisible = False
        '
        'LayoutControlItem7
        '
        Me.LayoutControlItem7.Control = Me.TxtFromMail
        Me.LayoutControlItem7.Location = New System.Drawing.Point(0, 254)
        Me.LayoutControlItem7.Name = "LayoutControlItem7"
        Me.LayoutControlItem7.Size = New System.Drawing.Size(493, 40)
        Me.LayoutControlItem7.TextVisible = False
        Me.LayoutControlItem7.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never
        '
        'frmAddToAllAccounts
        '
        Me.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.Appearance.Options.UseBackColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(8.0!, 16.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(523, 318)
        Me.Controls.Add(Me.LayoutControl1)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle
        Me.IconOptions.Icon = CType(resources.GetObject("frmAddToAllAccounts.IconOptions.Icon"), System.Drawing.Icon)
        Me.IconOptions.Image = Global.Best_Sender.My.Resources.Resources.smtpserverOlder
        Me.LookAndFeel.SkinName = "WXI"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Margin = New System.Windows.Forms.Padding(5, 3, 5, 3)
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.Name = "frmAddToAllAccounts"
        Me.Opacity = 0.99R
        Me.ShowInTaskbar = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "  Add to all Smtp accounts ..."
        CType(Me.DxErrorProvider1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.LayoutControl1.ResumeLayout(False)
        CType(Me.TxtFromMail.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TxtSubject.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TxtTag_2.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TxtFromName.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TxtTag_1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Root, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem6, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem7, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents DxErrorProvider1 As DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider
    Friend WithEvents LayoutControl1 As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents Root As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents TxtTag_1 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents LayoutControlItem1 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents EmptySpaceItem1 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents TxtFromName As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LayoutControlItem2 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents TxtTag_2 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents LayoutControlItem3 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents TxtSubject As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LayoutControlItem4 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents BntAdd As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LayoutControlItem5 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents BntNew As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LayoutControlItem6 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents TxtFromMail As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LayoutControlItem7 As DevExpress.XtraLayout.LayoutControlItem
End Class
