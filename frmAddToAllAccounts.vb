﻿Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid
Public Class frmAddToAllAccounts
    Private Sub FrmSMTPTester_Details_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Me.StartPosition = FormStartPosition.CenterScreen
        DxErrorProvider1.ClearErrors()
        BntNew_Click(Nothing, Nothing)
    End Sub
    Private Sub TxtSMTPServer_EditValueChanged(sender As Object, e As EventArgs)
        DxErrorProvider1.ClearErrors()
    End Sub
    Private Sub TxtEmail_EditValueChanged(sender As Object, e As EventArgs)
        DxErrorProvider1.ClearErrors()
    End Sub
    Private Sub TxtPassword_EditValueChanged(sender As Object, e As EventArgs)
        DxErrorProvider1.ClearErrors()
    End Sub
    Private Sub TxtPort_EditValueChanged(sender As Object, e As EventArgs)
        DxErrorProvider1.ClearErrors()
    End Sub
    Private Sub TxtFromName_EditValueChanged(sender As Object, e As EventArgs)
        DxErrorProvider1.ClearErrors()
    End Sub
    Private Sub TxtFromMail_EditValueChanged(sender As Object, e As EventArgs)
        DxErrorProvider1.ClearErrors()
    End Sub
    Private Sub TxtSubject_EditValueChanged(sender As Object, e As EventArgs)
        DxErrorProvider1.ClearErrors()
    End Sub





    Private Sub BntAdd_Click(sender As Object, e As EventArgs) Handles BntAdd.Click
        'If TxtFromName.Text.Trim = "" Then
        '    DxErrorProvider1.SetError(TxtFromName, "Enter from Name tag...!")
        '    TxtFromName.Focus()
        '    Exit Sub
        'End If
        'If TxtFromMail.Text.Trim = "" Then
        '    DxErrorProvider1.SetError(TxtFromMail, "Invalid from Email ...!")
        '    TxtFromMail.Focus()
        '    Exit Sub
        'End If
        'If TxtSubject.Text.Trim = "" Then
        '    DxErrorProvider1.SetError(TxtSubject, "Enter Subject tag...!")
        '    TxtSubject.Focus()
        '    Exit Sub
        'End If
        Dim result As DialogResult = XtraMessageBox.Show("You Like Add From Name and Subject For All SMTP", "Confirm", MessageBoxButtons.YesNo, MessageBoxIcon.Information, MessageBoxDefaultButton.Button2)
        If result = DialogResult.No Then Exit Sub
        Try
            For i = 0 To LS_SenderSmtp.Count - 1
                If Not String.IsNullOrEmpty(TxtFromName.Text.Trim) Then
                    LS_SenderSmtp(i).smtpfromname = TxtFromName.Text.Trim
                End If
                If Not String.IsNullOrEmpty(TxtFromMail.Text.Trim) Then
                    LS_SenderSmtp(i).smtpfrommail = TxtFromMail.Text.Trim
                End If
                If Not String.IsNullOrEmpty(TxtSubject.Text.Trim) Then
                    LS_SenderSmtp(i).smtpsubject = TxtSubject.Text.Trim
                End If
                LS_SenderSmtp(i).tag1 = TxtTag_1.Text.Trim
                LS_SenderSmtp(i).tag2 = TxtTag_2.Text.Trim
            Next
            ' تحديث البيانات بطريقة جذرية لضمان ظهور البيانات في جميع الأعمدة
            Try
                ' منع تحديث واجهة المستخدم أثناء تحديث البيانات لتحسين الأداء
                frmEmailSender.GridView1.BeginUpdate()

                ' 1. تطبيق التغييرات على مصدر البيانات
                frmEmailSender.GridControl1.DataSource = Nothing
                frmEmailSender.GridControl1.DataSource = LS_SenderSmtp

                frmEmailSender.Refresh()

                ' 3. تطبيق التنسيق المخصص على الأعمدة المحدثة
                ' هذا يضمن أن الأعمدة smtpfrommail و smtpsubject ستظهر بالتنسيق المناسب
                For i As Integer = 0 To frmEmailSender.GridView1.RowCount - 1
                    frmEmailSender.GridView1.FocusedRowHandle = i
                    frmEmailSender.GridView1.UpdateCurrentRow()
                Next

                ' 4. إجبار GridView على إعادة رسم جميع الخلايا
                frmEmailSender.GridView1.LayoutChanged()
            Finally
                ' إعادة تمكين تحديث واجهة المستخدم
                frmEmailSender.GridView1.EndUpdate()
            End Try
            ' إعادة تعيين حقول الإدخال
            BntNew_Click(Nothing, Nothing)


            ' إغلاق النموذج
            Me.Dispose()
        Catch ex As Exception
            '' عرض رسالة Error مفصلة للمستخدم
            'DevExpress.XtraEditors.XtraMessageBox.Show($"حدث Error أثناء تحديث البيانات: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            '' تسجيل الError في ملف السجل إذا كان ذلك مناسبًا
            'Console.WriteLine($"Error in BntAdd_Click: {ex.Message}\nStackTrace: {ex.StackTrace}")
        Finally
            ' التأكد من تحديث واجهة المستخدم حتى في حالة حدوث Error
            Try
                frmEmailSender.GridControl1.RefreshDataSource()
                frmEmailSender.GridView1.RefreshData()
            Catch refreshEx As Exception
                ' تجاهل أي أخطاء قد تحدث أثناء محاولة التحديث النهائية
            End Try
        End Try
    End Sub


    Private Sub TxtTag_1_SelectedIndexChanged(sender As Object, e As EventArgs) Handles TxtTag_1.SelectedIndexChanged
        DxErrorProvider1.ClearErrors()
        If TxtTag_1.SelectedIndex >= 0 Then
            If TxtTag_1.SelectedItem.ToString() = "Receiver Email" Then
                TxtFromName.Text = "[-Email-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Encoded Email 64" Then
                TxtFromName.Text = "[-Email64-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Start Name With Upper Case" Then
                TxtFromName.Text = "[-UCase-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Add Link" Then
                TxtFromName.Text = "[-Link-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Send With Logo" Then
                TxtFromName.Text = "[-Logo-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Character 3" Then
                TxtFromName.Text = "[-RCh3-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Character 4" Then
                TxtFromName.Text = "[-RCh4-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Character 5" Then
                TxtFromName.Text = "[-RCh5-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Character 6" Then
                TxtFromName.Text = "[-RCh6-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Character 7" Then
                TxtFromName.Text = "[-RCh7-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Number 3" Then
                TxtFromName.Text = "[-RN3-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Number 4" Then
                TxtFromName.Text = "[-RN4-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Number 5" Then
                TxtFromName.Text = "[-RN5-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Number 6" Then
                TxtFromName.Text = "[-RN6-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Number 7" Then
                TxtFromName.Text = "[-RN7-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random IP" Then
                TxtFromName.Text = "[-IP-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random IP China" Then
                TxtFromName.Text = "[-IPChina-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Show The Domain" Then
                TxtFromName.Text = "[-Domain-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Show Name" Then
                TxtFromName.Text = "[-Name-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Show Company Name" Then
                TxtFromName.Text = "[-CompanyName-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Show Date" Then
                TxtFromName.Text = "[-Date-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Show Time" Then
                TxtFromName.Text = "[-Time-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Show Date Tomorrow" Then
                TxtFromName.Text = "[-DateTomorrow-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Country" Then
                TxtFromName.Text = "[-RCountry-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Browser" Then
                TxtFromName.Text = "[-RandomBrowser-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Show Random Fake phone Numbers" Then
                TxtFromName.Text = "[-FakePhone-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Show Random Fake Email Address Office" Then
                TxtFromName.Text = "[-FakeEmail-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Show Random Street New York" Then
                TxtFromName.Text = "[-NewYork-]"
            End If
        End If
    End Sub

    Private Sub TxtTag_2_SelectedIndexChanged(sender As Object, e As EventArgs) Handles TxtTag_2.SelectedIndexChanged
        DxErrorProvider1.ClearErrors()
        If TxtTag_2.SelectedIndex >= 0 Then
            If TxtTag_2.SelectedItem.ToString() = "Receiver Email" Then
                TxtSubject.Text = "[-Email-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Encoded Email 64" Then
                TxtSubject.Text = "[-Email64-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Start Name With Upper Case" Then
                TxtSubject.Text = "[-UCase-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Add Link" Then
                TxtSubject.Text = "[-Link-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Send With Logo" Then
                TxtSubject.Text = "[-Logo-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Character 3" Then
                TxtSubject.Text = "[-RCh3-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Character 4" Then
                TxtSubject.Text = "[-RCh4-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Character 5" Then
                TxtSubject.Text = "[-RCh5-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Character 6" Then
                TxtSubject.Text = "[-RCh6-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Character 7" Then
                TxtSubject.Text = "[-RCh7-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Number 3" Then
                TxtSubject.Text = "[-RN3-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Number 4" Then
                TxtSubject.Text = "[-RN4-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Number 5" Then
                TxtSubject.Text = "[-RN5-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Number 6" Then
                TxtSubject.Text = "[-RN6-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Number 7" Then
                TxtSubject.Text = "[-RN7-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random IP" Then
                TxtSubject.Text = "[-IP-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random IP China" Then
                TxtSubject.Text = "[-IPChina-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Show The Domain" Then
                TxtSubject.Text = "[-Domain-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Show Name" Then
                TxtSubject.Text = "[-Name-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Show Company Name" Then
                TxtSubject.Text = "[-CompanyName-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Show Date" Then
                TxtSubject.Text = "[-Date-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Show Time" Then
                TxtSubject.Text = "[-Time-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Show Date Tomorrow" Then
                TxtSubject.Text = "[-DateTomorrow-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Country" Then
                TxtSubject.Text = "[-RCountry-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Browser" Then
                TxtSubject.Text = "[-RandomBrowser-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Show Random Fake phone Numbers" Then
                TxtSubject.Text = "[-FakePhone-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Show Random Fake Email Address Office" Then
                TxtSubject.Text = "[-FakeEmail-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Show Random Street New York" Then
                TxtSubject.Text = "[-NewYork-]"
            End If
        End If
    End Sub

    Private Sub BntNew_Click(sender As Object, e As EventArgs) Handles BntNew.Click
        DxErrorProvider1.ClearErrors()
        TxtTag_1.SelectedIndex = -1
        TxtFromMail.Text = ""
        TxtFromName.Text = ""
        TxtTag_2.SelectedIndex = -1
        TxtSubject.Text = ""
        Me.ActiveControl = TxtFromName
    End Sub
End Class