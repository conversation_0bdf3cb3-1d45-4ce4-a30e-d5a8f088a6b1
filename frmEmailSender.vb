Imports System.ComponentModel
Imports System.IO
Imports System.Linq
Imports System.Net
Imports System.Net.Mail
Imports System.Net.Mime
Imports System.Reflection
Imports System.Text
Imports System.Text.RegularExpressions
Imports System.Threading
Imports System.Threading.Tasks
Imports System.Windows
Imports System.Windows.Controls
Imports CoreHtmlToImage
Imports DevExpress.XtraBars
Imports DevExpress.XtraBars.Commands
Imports DevExpress.XtraBars.Docking2010
Imports DevExpress.XtraBars.Docking2010.Views.Tabbed
Imports DevExpress.XtraBars.Ribbon
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid
Imports DevExpress.XtraGrid.Views.Base
Imports DevExpress.XtraGrid.Views.Grid
Imports DevExpress.XtraLayout
Imports MailKit.Net.Imap
Imports MessagingToolkit.QRCode.Codec



' تم إزالة الفئات الجزئية لأننا نستخدم الطريقة التقليدية للتحقق من SMTP
Public Class frmEmailSender
    Dim urlPattern As String = "^(https?://)?(www\.)?[\w-]+(\.[a-z]{2,})+(/[^\s]*)?$"
    Dim urlRegex As New Regex(urlPattern, RegexOptions.IgnoreCase)
    Dim ResizedImage As Image
    Dim PROGRESS As Integer = 0
    Dim CheckAll As Boolean = False
    Dim myValue As Boolean = False
    Dim _lock As Object = New Object()
    Dim isLogoRemoved As Integer = -1
    Dim totalMailsLabel As Integer = 0
    Dim totalstmpLabel As Integer = 0
    Private docManager As DocumentManager
    Private notificationIcon As BarStaticItem
    Dim Child_frmEditLetter As New frmEditLetter
    Private useCustomColors As Boolean = False
    Private ChngFlg As String = ""
    '===============================================
    ' تعريف الروبين علشان تتحرك ما التابات
    Public Property RelatedRibbonPage As RibbonPage
    '===============================================
#Region "User Defined"
    Private Function getImage(domain As String) As System.IO.Stream
        Dim _WebRequest As System.Net.WebRequest = Nothing
        Dim _NormalImage As System.IO.Stream = Nothing
TryAgain_:
        Try
            _WebRequest = WebRequest.Create("https://www.google.com/s2/favicons?sz=64&domain_url=" + domain)
        Catch ex As Exception
            ''Windows.Forms.MessageBox.Show(ex.Message)
            Return Nothing
        End Try
        Try
            _NormalImage = _WebRequest.GetResponse().GetResponseStream()
        Catch ex As Exception
            If ex.Message.Contains("404") Then
                domain = domain.Replace("www.", "")
                domain = "www." + domain.Substring(domain.IndexOf(".") + 1)
                Dim count = domain.Split(".").Length - 1
                If count = 1 Then Return Nothing Else GoTo TryAgain_
            End If
            Return Nothing
        End Try
        Return _NormalImage
    End Function
#End Region
    Dim isRunning_ As Boolean = False
    Dim isStopped_ As Boolean = False
    Dim Letter As Boolean = False
    Dim Tick_ As Integer = 0
    Dim tt_ As New Windows.Controls.ToolTip
    Dim txtattachTextGlobal As String
    Dim filepath As String = ""
    Dim TempPath As String = My.Computer.FileSystem.SpecialDirectories.Temp & "\MailAttachmentFiles"
    Dim cancellationTokenSource As New CancellationTokenSource()
    Dim successNum As Integer = 0
    Dim failNum As Integer = 0
    ReadOnly FunctionList As List(Of String) = New List(Of String) From {"[-Email64-]", "[-Email-]", "[-IP-]", "[-IPChina-]", "[-Domain-]", "[-Name-]", "[-Date-]", "[-DateTomorrow-]",
        "[-RandomBrowser-]", "[-RCountry-]", "[-FakePhone-]", "[-FakeEmail-]", "[-NewYork-]", "[-UCase-]", "[-Link-]", "[-Logo-]", "[-RN1-]", "[-RN2-]", "[-RN3-]", "[-RN4-]",
        "[-RN5-]", "[-RN6-]", "[-QRCode-]", "[-CompanyName-]", "[-Time-]", "[-RN7-]", "[-RN8-]", "[-RN9-]", "[-RN10-]", "[-RN12-]", "[-RCh1-]", "[-RCh2-]", "[-RCh3-]", "[-RCh4-]", "[-RCh5-]", "[-RCh6-]", "[-RCh7-]", "[-RCh8-]"}

    ' ✅ قوائم التدوير التلقائي الجديدة - New Automatic Rotation Lists
    ' هذه القوائم تحتوي على البيانات التي سيتم اختيارها عشوائياً عند الإرسال
    ' These lists contain data that will be randomly selected during sending
    Private SubjectsList As New List(Of String)      ' قائمة المواضيع - Subjects list
    Private FromNamesList As New List(Of String)     ' قائمة أسماء المرسل - From names list
    Private FromEmailsList As New List(Of String)    ' قائمة بريد المرسل - From emails list
    Private Random As New Random()                   ' مولد الأرقام العشوائية - Random number generator
    Private Sub trmfadein_Tick(sender As Object, e As EventArgs) Handles trmfadein.Tick
        Me.Opacity = Me.Opacity + 0.02
        If Me.Opacity = 1 Then
            Me.Opacity = 98 / 100
            trmfadein.Enabled = False
            trmfadein.Stop()
        End If
    End Sub
    Private Sub trmfadeout_Tick(sender As Object, e As EventArgs) Handles trmfadeout.Tick
        Me.Opacity = Me.Opacity - 0.02
        If Me.Opacity < 0.001 Then
            Me.Dispose()
        End If
    End Sub
    'Sub RESIZEIMAGE()
    '    If Pic_QRCode.Image Is Nothing Then Exit Sub
    '    Pic_QRCode.Image = Image.FromFile(Pic_QRCode.Tag.ToString())
    '    Dim NewSize As New System.Drawing.Size(Convert.ToInt16(txtWidth.Text), Convert.ToInt16(txtHeight.Text))
    '    ResizedImage = New Bitmap(Pic_QRCode.Image, NewSize)
    '    Pic_QRCode.Image = ResizedImage
    '    Dim NSize As Integer = Convert.ToInt16(txtWidth.Text) + Convert.ToInt16(txtHeight.Text)
    '    If NSize < Me.Tag Then
    '        Pic_QRCode.SizeMode = PictureBoxSizeMode.CenterImage
    '    Else
    '        Pic_QRCode.SizeMode = PictureBoxSizeMode.StretchImage
    '    End If
    'End Sub
    'Private Sub txtWidth_TextChanged(sender As Object, e As EventArgs) Handles txtWidth.TextChanged
    '    If Val(txtWidth.Text) > 0 And Val(txtHeight.Text) > 0 Then RESIZEIMAGE()
    'End Sub
    'Private Sub txtHeight_TextChanged(sender As Object, e As EventArgs) Handles txtHeight.TextChanged
    '    If Val(txtWidth.Text) > 0 And Val(txtHeight.Text) > 0 Then RESIZEIMAGE()
    'End Sub
    Private Sub BunifuImageButton7_Click(sender As Object, e As EventArgs)
        Try
            ' حفظ الإعدادات
            My.Settings.LETTER = txtLetter.Text
            My.Settings.LETTER_LINK = txtNewLink.Text
            My.Settings.LETTER_CON_LINK = txt_LetterConvertorLink.Text
            My.Settings.SMTP_DATA_TABLE_XML = FuncSendMail.SerializeListToXml(LS_SenderSmtp)
            My.Settings.EMAILS_DATA_TABLE_XML = FuncSendMail.SerializeListToXml(LS_SenderMail)
            ' تشغيل المهمة في الخلفية لحساب MD5 وتحديث الإعدادات
            System.Threading.Tasks.Task.Run(Sub()
                                                Try
                                                    Dim mdf As String = FuncSendMail.GetSettingsMd5()
                                                    If My.Settings.SETTING_MDF <> mdf Then
                                                        My.Settings.SETTING_MDF = mdf
                                                        My.Settings.Save()
                                                    End If
                                                Catch ex As Exception
                                                    ' يمكنك تسجيل الخطأ إذا لزم الأمر
                                                End Try
                                            End Sub)
            ' مسح القوائم
            LS_SenderMail.Clear()
            LS_SenderSmtp.Clear()

        ' ✅ مسح القوائم الجديدة أيضاً
        SubjectsList.Clear()
        FromNamesList.Clear()
        FromEmailsList.Clear()
        SaveRotationListsToSettings()
            ' تحديث صورة Pic_Sender في frmMainMenu قبل الإغلاق
            Dim mainMenu As frmMain = Nothing
            ' البحث عن frmMainMenu إذا كان مفتوحًا (استخدام OpenForms بشكل صحيح)
            For Each frm As Form In System.Windows.Forms.Application.OpenForms
                If TypeOf frm Is frmMain Then
                    mainMenu = DirectCast(frm, frmMain)
                    Exit For
                End If
            Next
            ' إذا كان موجودًا، قم بتحديث صورة Pic_Sender بأمان
            If mainMenu IsNot Nothing Then
                If mainMenu.InvokeRequired Then
                    mainMenu.Invoke(New MethodInvoker(Sub()
                                                      End Sub))
                Else
                End If
            End If
            ' إغلاق النموذج الحالي
            Me.Dispose()
        Catch ex As Exception
            'MessageBox.Show("An error occurred while saving or updating: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    Private Sub frmEmailSender_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ' تهيئة تمركز XtraTabControl2 داخل XtraTabControl1
        InitializeCenteringControls()




        '==============================
        txtLetter.Text = EncoderVariables.EncodedLetter
        Try
            ' ✅ استرجاع قائمة SMTP إذا كانت موجودة
            Dim smtpCachedData = FuncSendMail.DeserializeXmlToList(Of SenderSmtpSettings)(My.Settings.SMTP_DATA_TABLE_XML)
            If smtpCachedData IsNot Nothing AndAlso smtpCachedData.Count > 0 Then
                If LS_SenderSmtp.Count > 0 Then
                    smtpCachedData.ForEach(Sub(x)
                                               If LS_SenderSmtp.All(Function(c) x.smtpemail <> c.smtpemail AndAlso x.smtppassword <> c.smtppassword AndAlso x.smtphost <> c.smtphost AndAlso x.smtpport <> c.smtpport AndAlso x.smtpssl <> c.smtpssl) Then
                                                   LS_SenderSmtp.Add(x)
                                               End If
                                           End Sub)
                Else
                    LS_SenderSmtp = smtpCachedData
                End If
                lbltotalstmp.Text = $"Total Smtps: {LS_SenderSmtp.Count}"
                GridControl1.RefreshDataSource()
            End If
            ' ✅ استرجاع قائمة البريد الإلكتروني إذا كانت موجودة
            Dim emailsCachedData = FuncSendMail.DeserializeXmlToList(Of SenderMailItem)(My.Settings.EMAILS_DATA_TABLE_XML)
            If emailsCachedData IsNot Nothing AndAlso emailsCachedData.Count > 0 Then
                If LS_SenderMail.Count > 0 Then
                    emailsCachedData.ForEach(Sub(x)
                                                 If LS_SenderMail.All(Function(c) x.emailaddress <> c.emailaddress) Then
                                                     LS_SenderMail.Add(x)
                                                 End If
                                             End Sub)
                Else
                    LS_SenderMail = emailsCachedData
                End If
                lblTotal.Text = $"Total Mails: {LS_SenderMail.Count}"
                GridControl2.RefreshDataSource()
            End If
            ' ✅ تحديث مصادر البيانات
            GridControl1.DataSource = LS_SenderSmtp
            GridControl2.DataSource = LS_SenderMail

            ' ✅ استرجاع القوائم الجديدة من الإعدادات
            LoadRotationListsFromSettings()
            ' Set Binance-inspired appearance for GridControl1
            ' Main background color - dark gray/black (RGB 18, 22, 28)
            GridView1.Appearance.Empty.BackColor = Color.FromArgb(18, 22, 28)
            GridView1.Appearance.Row.BackColor = Color.FromArgb(18, 22, 28)
            GridView1.Appearance.FocusedRow.BackColor = Color.FromArgb(18, 22, 28)
            GridView1.Appearance.SelectedRow.BackColor = Color.FromArgb(18, 22, 28)
            GridView1.Appearance.HideSelectionRow.BackColor = Color.FromArgb(18, 22, 28)
            ' Set text color to white
            GridView1.Appearance.Row.ForeColor = Color.FromArgb(255, 255, 255)
            GridView1.Appearance.Row.Options.UseForeColor = True
            ' Configure alternate row color - darker gray (RGB 30, 35, 41)
            GridView1.OptionsView.EnableAppearanceEvenRow = True
            GridView1.OptionsView.EnableAppearanceOddRow = True
            GridView1.Appearance.EvenRow.BackColor = Color.FromArgb(18, 22, 28)
            GridView1.Appearance.EvenRow.Options.UseBackColor = True
            GridView1.Appearance.OddRow.BackColor = Color.FromArgb(30, 35, 41)
            GridView1.Appearance.OddRow.Options.UseBackColor = True
            ' Configure header panel appearance
            GridView1.Appearance.HeaderPanel.BackColor = Color.FromArgb(18, 22, 28)
            GridView1.Appearance.HeaderPanel.ForeColor = Color.FromArgb(160, 174, 192)
            GridView1.Appearance.HeaderPanel.Font = New Font(GridView1.Appearance.HeaderPanel.Font.FontFamily, GridView1.Appearance.HeaderPanel.Font.Size, System.Drawing.FontStyle.Bold)
            GridView1.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
            GridView1.Appearance.HeaderPanel.Options.UseBackColor = True
            GridView1.Appearance.HeaderPanel.Options.UseForeColor = True
            GridView1.Appearance.HeaderPanel.Options.UseFont = True
            GridView1.Appearance.HeaderPanel.Options.UseTextOptions = True
            ' Configure hover row appearance - RGB(44, 47, 54)
            GridView1.Appearance.HotTrackedRow.BackColor = Color.FromArgb(44, 47, 54)
            GridView1.Appearance.HotTrackedRow.Options.UseBackColor = True
            ' Configure hover row appearance
            ' Configure selected/focused row - RGB(47, 52, 59)
            GridView1.Appearance.FocusedRow.BackColor = Color.FromArgb(47, 52, 59)
            GridView1.Appearance.FocusedRow.Options.UseBackColor = True
            GridView1.Appearance.SelectedRow.BackColor = Color.FromArgb(47, 52, 59)
            GridView1.Appearance.SelectedRow.Options.UseBackColor = True
            ' Remove grid lines
            GridView1.OptionsView.ShowHorizontalLines = DevExpress.Utils.DefaultBoolean.False
            GridView1.OptionsView.ShowVerticalLines = DevExpress.Utils.DefaultBoolean.False
            ' Set font to Segoe UI with 10pt size
            Dim gridFont As New Font("Segoe UI", 10)
            GridView1.Appearance.Row.Font = gridFont
            GridView1.Appearance.Row.Options.UseFont = True
            ' Reduce row height for more compact display
            GridView1.RowHeight = 28
            ' Set the background color for GridColumn1 (id column)
            If GridView1.Columns("id") IsNot Nothing Then
                GridView1.Columns("id").AppearanceCell.BackColor = Color.FromArgb(18, 22, 28)
                GridView1.Columns("id").AppearanceCell.Options.UseBackColor = True
                ' Force the column to use custom drawing
                GridView1.Columns("id").OptionsColumn.ShowInCustomizationForm = False
                GridView1.Columns("id").OptionsColumn.AllowEdit = False
                GridView1.Columns("id").OptionsColumn.ReadOnly = True
            End If
            ' Configure selection settings
            GridView1.OptionsSelection.EnableAppearanceFocusedRow = True
            GridView1.OptionsSelection.EnableAppearanceHideSelection = False
            GridView1.OptionsSelection.MultiSelect = False
            GridView1.OptionsSelection.MultiSelectMode = DevExpress.XtraGrid.Views.Grid.GridMultiSelectMode.RowSelect
            ' Apply similar styling to GridView2
            GridView2.Appearance.Empty.BackColor = Color.FromArgb(18, 22, 28)
            GridView2.Appearance.Row.BackColor = Color.FromArgb(18, 22, 28)
            GridView2.Appearance.FocusedRow.BackColor = Color.FromArgb(47, 52, 59)
            GridView2.Appearance.SelectedRow.BackColor = Color.FromArgb(47, 52, 59)
            GridView2.Appearance.HideSelectionRow.BackColor = Color.FromArgb(18, 22, 28)
            ' Configure GridView2 alternate rows
            GridView2.OptionsView.EnableAppearanceEvenRow = True
            GridView2.OptionsView.EnableAppearanceOddRow = True
            GridView2.Appearance.EvenRow.BackColor = Color.FromArgb(18, 22, 28)
            GridView2.Appearance.EvenRow.Options.UseBackColor = True
            GridView2.Appearance.OddRow.BackColor = Color.FromArgb(30, 35, 41)
            GridView2.Appearance.OddRow.Options.UseBackColor = True
            ' Set text color for GridView2
            GridView2.Appearance.Row.ForeColor = Color.FromArgb(255, 255, 255)
            GridView2.Appearance.Row.Options.UseForeColor = True
            GridView2.Appearance.Row.Font = gridFont
            GridView2.Appearance.Row.Options.UseFont = True
            ' Configure GridView2 header
            GridView2.Appearance.HeaderPanel.BackColor = Color.FromArgb(18, 22, 28)
            GridView2.Appearance.HeaderPanel.ForeColor = Color.FromArgb(160, 174, 192)
            GridView2.Appearance.HeaderPanel.Font = New Font(GridView2.Appearance.HeaderPanel.Font.FontFamily, GridView2.Appearance.HeaderPanel.Font.Size, System.Drawing.FontStyle.Bold)
            GridView2.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
            GridView2.Appearance.HeaderPanel.Options.UseBackColor = True
            GridView2.Appearance.HeaderPanel.Options.UseForeColor = True
            GridView2.Appearance.HeaderPanel.Options.UseFont = True
            GridView2.Appearance.HeaderPanel.Options.UseTextOptions = True
            ' Configure GridView2 hover row
            GridView2.Appearance.HotTrackedRow.BackColor = Color.FromArgb(44, 47, 54)
            GridView2.Appearance.HotTrackedRow.Options.UseBackColor = True
            ' Configure hover row appearance
            ' Remove grid lines for GridView2
            GridView2.OptionsView.ShowHorizontalLines = DevExpress.Utils.DefaultBoolean.False
            GridView2.OptionsView.ShowVerticalLines = DevExpress.Utils.DefaultBoolean.False
            ' Reduce row height for GridView2
            GridView2.RowHeight = 28
            ' Disable row indicators and focus rectangles
            GridView1.OptionsView.ShowIndicator = False
            GridView2.OptionsView.ShowIndicator = False
            GridView1.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.None
            GridView2.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.None
            ' Configure the SMTP status column to show images
            If GridView1.Columns("smtpstatus") IsNot Nothing Then
                GridView1.Columns("smtpstatus").OptionsColumn.AllowEdit = False
                GridView1.Columns("smtpstatus").AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
                ' Set up the column to show icons
                GridView1.OptionsView.ShowIndicator = False
                ' Ensure the events are attached
                AddHandler GridView1.RowCellStyle, AddressOf GridView1_RowCellStyle
                AddHandler GridView1.CustomRowCellEdit, AddressOf GridView1_CustomRowCellEdit
            End If
            'GridView1.FocusedRowHandle = 1
            'GridView2.FocusedRowHandle = 1
            ' ✅ تحميل باقي الإعدادات
            If My.Settings.LETTER IsNot Nothing Then txtLetter.Text = My.Settings.LETTER
            If My.Settings.LETTER_LINK IsNot Nothing Then txtNewLink.Text = My.Settings.LETTER_LINK
            If My.Settings.LETTER_CON_LINK IsNot Nothing Then txt_LetterConvertorLink.Text = My.Settings.LETTER_CON_LINK
            lblTrcThreads.Text = trcThreads.Value
            ' ✅ تعيين التبويبة الافتراضية وإخفاء بعض العناصر
            XtraTabControl1.SelectedTabPageIndex = 0
            pnlResult.Visible = False
            pnlWait.Visible = False
            ' ✅ تنظيف بعض الحقول
            'bntClearLetter_Click(Nothing, Nothing)
            'BntResetLetterLogo_Click(Nothing, Nothing)
            BntRemoveAttachment_Click(Nothing, Nothing)
            'bntResetLetterConvertor_Click(Nothing, Nothing)
            Label13.Text = "Data loaded Successfully"
            Pic_QRCode.Image = Nothing
            'DevExpress.XtraEditors.XtraMessageBox.Show("Data loaded Successfully", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Catch ex As Exception
            DevExpress.XtraEditors.XtraMessageBox.Show("Error while loading data: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    Private Sub Timer1_Tick(sender As Object, e As EventArgs) Handles Timer1.Tick
        If Tick_ Mod 2 = 0 Then
            lbl1.Visible = True
        Else
            lbl1.Visible = False
        End If
        Tick_ = Tick_ + 1
    End Sub
    Public Sub Add_SMTP_Account()
        If isRunning_ = True Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Cannot add now, Sending process is running...!", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        LS_SenderSmtp_IsEditMode = False
        frmAddSmtpServer.ShowDialog()
        AddHandler GridView1.RowCellStyle, AddressOf GridView1_RowCellStyle
    End Sub
    Public Sub DoNew()
        ' عرض رسالة تأكيد قبل إعادة ضبط الإعدادات
        Dim result As DialogResult = DevExpress.XtraEditors.XtraMessageBox.Show("Do you want to reset the settings?", "Confirmation", MessageBoxButtons.YesNo, MessageBoxIcon.Question)
        ' إذا اختار المستخدم "No"، لا يتم تنفيذ الكود
        If result = DialogResult.No Then
            Exit Sub
        End If
        ' تنفيذ الكود إذا اختار "Yes"
        If isRunning_ = True Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Sending process is running...!", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        isRunning_ = False
        XtraTabControl1.SelectedTabPageIndex = 0
        pnlResult.Visible = False
        pnlWait.Visible = False
        LS_SenderSmtp.Clear()
        LS_SenderMail.Clear()

        ' ✅ مسح القوائم الجديدة
        SubjectsList.Clear()
        FromNamesList.Clear()
        FromEmailsList.Clear()
        SaveRotationListsToSettings()

        ' إعادة تعيين عناوين الأعمدة
        ResetColumnCaption(FieldType.Subject)
        ResetColumnCaption(FieldType.FromName)
        ResetColumnCaption(FieldType.FromMail)

        My.Settings.SMTP_DATA_TABLE_XML = FuncSendMail.SerializeListToXml(LS_SenderSmtp)
        My.Settings.EMAILS_DATA_TABLE_XML = FuncSendMail.SerializeListToXml(LS_SenderMail)
        lblTotal.Text = ""
        lbltotalstmp.Text = ""
        GridControl1.RefreshDataSource()
        GridControl2.RefreshDataSource()
        'LS_SenderMail.Clear()
        'bntClearLetter_Click(Nothing, Nothing)
        BntResetLetterLogo_Click(Nothing, Nothing)
        BntRemoveAttachment_Click(Nothing, Nothing)
        ChkLogo_CheckedChanged(Nothing, Nothing)
        Letter = False
        ProgressBarControl1.Properties.Step = 1
        ProgressBarControl1.Position = 0
        ProgressBarControl1.EditValue = 0
        ProgressBarControl1.Properties.Maximum = MailTable.Rows.Count
        txtLetter.Clear()
        txtLetterPath.Clear()
        txtNewLink.Clear()
        txt_LetterConvertorLink.Clear()
        txt_limitSend.Clear()
        txtCode.Clear()
        txtattach.Clear()
        ' مسح الصور إذا وجدت
        If piclogo.Image IsNot Nothing Then
            piclogo.Image.Dispose()
            piclogo.Image = Nothing
        End If
        If Pic_QRCode.Image IsNot Nothing Then
            Pic_QRCode.Image.Dispose()
            Pic_QRCode.Image = Nothing
        End If
    End Sub
    Private Sub Bnt_Remove_ButtonClick(sender As Object, e As DevExpress.XtraEditors.Controls.ButtonPressedEventArgs) Handles Bnt_Remove.ButtonClick
        If isRunning_ = True Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Cannot remove now, Sending process is running...!", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        Dim result As DialogResult = XtraMessageBox.Show("Are you sure you want to remove the current Smtp account?", "Question", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2)
        If result = DialogResult.No Then Exit Sub
        Dim SelectedRowHandles As Integer() = GridView1.GetSelectedRows()
        Dim id_ = GridView1.GetRowCellValue(SelectedRowHandles(0), GridView1.Columns("id"))
        Try
            Dim foundRow = LS_SenderSmtp.FirstOrDefault(Function(x) x.id = id_)
            LS_SenderSmtp.Remove(foundRow)
            For i As Integer = 0 To LS_SenderSmtp.Count - 1
                LS_SenderSmtp(i).id = i + 1
            Next
            '=================================================================
            ' Refresh Smtp gird
            GridControl1.RefreshDataSource()
            lbltotalstmp.Text = $"Total Smtps: {LS_SenderSmtp.Count}"
            DevExpress.XtraEditors.XtraMessageBox.Show("The Smtp account was Successfully removed...", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Catch ex As Exception
            DevExpress.XtraEditors.XtraMessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    Private Sub BNT_Details_ButtonClick(sender As Object, e As Controls.ButtonPressedEventArgs) Handles BNT_Details.ButtonClick
        If isRunning_ = True Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Cannot show details now, Sending process is running...!", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        For i = 0 To GridView1.DataRowCount - 1
            LS_SenderSmtp(i).smtpssl = CBool(GridView1.GetRowCellValue(i, "smtpssl"))
        Next
        Dim SelectedRowHandles As Integer() = GridView1.GetSelectedRows()
        Dim id_ = GridView1.GetRowCellValue(SelectedRowHandles(0), GridView1.Columns("id"))
        LS_SenderSmtp_IsEditMode = True
        frmAddSmtpServer.Tag = id_
        frmAddSmtpServer.ShowDialog()
    End Sub
    Public Sub Clear_SMTP_List()
        If isRunning_ = True Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Cannot clear the list, Sending process is running...!", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        lbltotalstmp.Text = ""
        SmptTestWait.Visible = False
        LS_SenderSmtp.Clear()
        GridControl1.RefreshDataSource()
        My.Settings.SMTP_DATA_TABLE_XML = ""
    End Sub
    Private needMoveLastRow As Boolean = True
    Private Sub GridView1_RowLoaded(sender As Object, e As DevExpress.XtraGrid.Views.Base.RowEventArgs) Handles GridView1.RowLoaded
        Dim view As ColumnView = TryCast(sender, ColumnView)
        If needMoveLastRow Then
            needMoveLastRow = False
            view.MoveLast()
        End If
    End Sub
    Public Sub Add_Mail_List()
        lblTotal.Text = ""
        PictureBox1.Visible = False
        Dim totalMailCount As Integer = 0
        If isRunning_ Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Cannot Add now, Sending process is running...!", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        Dim openFileDialog As New OpenFileDialog()
        openFileDialog.Filter = "Text Files (*.txt)|*.txt"
        If openFileDialog.ShowDialog() = DialogResult.OK Then
            Try
                ' إخفاء ProgressBarControl2 قبل بدء العملية
                ProgressBarControl2.Visible = False
                'CreateEmailTable(True)
                Dim filePath As String = openFileDialog.FileName
                Dim Emails = New HashSet(Of String)(File.ReadAllLines(filePath)).ToList()
                Dim Counter_ As Integer = 0
                Dim totalLines As Integer = Emails.Count()
                ' إظهار ProgressBarControl2 بعد بدء العملية
                ProgressBarControl2.Visible = True
                ProgressBarControl2.Properties.Maximum = 100
                ProgressBarControl2.Properties.Step = 1
                Dim startTime As DateTime = DateTime.Now
                For Each line As String In Emails
                    If String.IsNullOrEmpty(line) Then Continue For
                    Dim item As New SenderMailItem()
                    With item
                        item.id = Counter_ + 1
                        item.emailaddress = line
                        item.deliverystatus = "Ready to Receive"
                        item.responsemessage = "----------"
                        item.date = "----------"
                        item.time = "----------"
                    End With
                    LS_SenderMail.Add(item)
                    Counter_ += 1
                    If Counter_ Mod 100 = 0 OrElse Counter_ >= Emails.Count Then
                        lblTotal.Text = $"Total Mails: {Counter_}"
                        Dim progressPercentage As Integer = (Counter_ * 100) \ totalLines
                        ProgressBarControl2.Position = progressPercentage
                        ProgressBarControl2.Refresh()
                    End If
                Next
                lblTotal.Text = $"Total Mails: {Counter_}"
                Dim endTime As DateTime = DateTime.Now
                Dim elapsedTime As TimeSpan = endTime - startTime
                Dim elapsedTimeString As String = String.Format("{0:hh\:mm\:ss}", elapsedTime)
                ProgressBarControl2.Position = 100
                PictureBox1.Visible = True
                lblTotal.Visible = True
                ' إخفاء ProgressBarControl2 بعد الانتهاء
                ProgressBarControl2.Visible = False
                If LS_SenderMail.Count > 0 Then
                    GridControl2.RefreshDataSource()
                    AddHandler GridView2.RowCellStyle, AddressOf GridView2_RowCellStyle
                End If
                DevExpress.XtraEditors.XtraMessageBox.Show($"File uploaded Successfully in {elapsedTimeString}", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Catch ex As Exception
                DevExpress.XtraEditors.XtraMessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub
    Public Sub Clear_Mail_List()
        If isRunning_ = True Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Cannot clear the list, Sending process is running...!", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        lblTotal.Text = ""
        PictureBox1.Visible = False
        LS_SenderMail.Clear()
        GridControl2.RefreshDataSource()
        My.Settings.EMAILS_DATA_TABLE_XML = ""
    End Sub
    Private Sub RepositoryItemButtonEdit2_ButtonClick(sender As Object, e As DevExpress.XtraEditors.Controls.ButtonPressedEventArgs) Handles RepositoryItemButtonEdit2.ButtonClick
        If isRunning_ = True Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Cannot remove now, Sending process is running...!", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        Dim SelectedRowHandles As Integer() = GridView2.GetSelectedRows()
        Dim id_ = GridView2.GetRowCellValue(SelectedRowHandles(0), GridView2.Columns("id"))
        Try
            Dim foundRow = LS_SenderMail.FirstOrDefault(Function(x) x.id = id_)
            ' reomve item
            LS_SenderMail.Remove(foundRow)
            For i As Integer = 0 To LS_SenderMail.Count - 1
                LS_SenderMail(i).id = i + 1
            Next
            '=================================================================
            ' Refresh Smtp gird
            GridControl2.RefreshDataSource()
            'My.Settings.EMAILS_DATA_TABLE_XML = FuncSendMail.SerializeListToXml(LS_SenderMail)
            'My.Settings.Save()
        Catch ex As Exception
            DevExpress.XtraEditors.XtraMessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    Public Sub Add_Letter()
        'Dim ofd As New OpenFileDialog()
        'ofd.Filter = "HTML Files|*.html;*.htm"
        'If ofd.ShowDialog() = DialogResult.OK Then
        '    txtLetterPath.Text = ofd.FileName
        '    WebBrowser1.Navigate(ofd.FileName)
        '    Letter = True
        '    While WebBrowser1.ReadyState <> WebBrowserReadyState.Complete
        '        System.Windows.Forms.Application.DoEvents()
        '    End While
        '    Dim html As String = File.ReadAllText(ofd.FileName)
        '    txtLetter.Text = html
        '    isLogoRemoved = -1
        '    ChkLogo.Checked = False
        'End If
        Dim ofd As New OpenFileDialog()
        ofd.Filter = "HTML and Text Files|*.html;*.htm;*.txt|All Files|*.*"
        If ofd.ShowDialog() = DialogResult.OK Then
            txtLetterPath.Text = ofd.FileName
            Dim html As String = File.ReadAllText(ofd.FileName)
            txtLetter.Text = html
            isLogoRemoved = -1
            ChkLogo.Checked = False
        End If
    End Sub
    Public Sub Clear_Letter()
        txtLetter.Text = ""
        txtLetterPath.Text = ""
        Letter = False
        isLogoRemoved = -1
        ChkLogo.Checked = False
    End Sub
    Public Function GetLogoHtmlAndRes(path As String, Optional path2 As String = Nothing, Optional recipientEmail As String = Nothing) As Object
        Dim logoStream As IO.Stream = Nothing
        Dim htmlImageTag As String = String.Empty
        Dim linkedImage As LinkedResource = Nothing

        ' إذا كان CheckEditGetAutoLogoDomainNewNew مفعل ويوجد بريد مستقبل، احصل على شعار الدومين الخاص بالمستقبل

        If CheckEditGetAutoLogoDomainNewNew IsNot Nothing AndAlso CheckEditGetAutoLogoDomainNewNew.Checked AndAlso Not String.IsNullOrEmpty(recipientEmail) AndAlso recipientEmail.Contains("@") AndAlso txtLetter IsNot Nothing AndAlso txtLetter.Text.Contains("[-Logo-]") Then
            logoStream = getImage("www." + recipientEmail.Substring(recipientEmail.IndexOf("@") + 1))
            If logoStream IsNot Nothing Then
                linkedImage = New LinkedResource(logoStream)
                htmlImageTag = FuncSendMail.GetHtmlImageTag(FuncSendMail.ConvertToBase64(logoStream))
                linkedImage.ContentId = "MyPic"
                linkedImage.ContentType = New ContentType(MediaTypeNames.Image.Jpeg)
                Return New With {Key .LinkedImage = linkedImage, Key .HtmlImageTag = htmlImageTag}
            End If
        End If

        ' الكود الأصلي للحصول على شعار المرسل
        If String.IsNullOrEmpty(path2) Then
            logoStream = getImage("www." + path.Substring(path.IndexOf("@") + 1))
            If (logoStream Is Nothing) Then
                Return Nothing
            End If
            linkedImage = New LinkedResource(logoStream)
            htmlImageTag = FuncSendMail.GetHtmlImageTag(FuncSendMail.ConvertToBase64(logoStream))
        Else
            linkedImage = New LinkedResource(lblLogoTitle.Tag.ToString())
            htmlImageTag = FuncSendMail.GetHtmlImageTag(FuncSendMail.ConvertToBase64(lblLogoTitle.Tag.ToString()))
        End If
        linkedImage.ContentId = "MyPic"
        linkedImage.ContentType = New ContentType(MediaTypeNames.Image.Jpeg)
        Return New With {Key .LinkedImage = linkedImage, Key .HtmlImageTag = htmlImageTag}
    End Function
    Private Sub BackgroundWorker1_DoWork(sender As Object, e As System.ComponentModel.DoWorkEventArgs) Handles BackgroundWorker1.DoWork
        If trcThreads.EditValue > 1 Then
            Dim result As DialogResult = XtraMessageBox.Show("Increasing threads for the send process might block your SMTP. Do you want to continue?", "Warning", MessageBoxButtons.YesNo, MessageBoxIcon.Warning)
            If result = DialogResult.No Then
                Exit Sub
            End If
        End If
        CheckForIllegalCrossThreadCalls = False
        '================== متغيرات عثمان
        Dim SMTPUserName As String = ""
        Dim SMTPPassword As String = ""
        Dim SMTPPort As String = ""
        Dim SMTPHost As String = ""
        Dim FromMail As String = ""
        Dim FromMailName As String = ""
        Dim FromMailSubject As String = ""
        If Not isStopped_ Then
            successNum = 0
            failNum = 0
        End If
        Dim counter As Integer = 1
        Dim SmtpStatus_ As String = ""
        Dim ssl_ As Boolean = False
        ProgressBarControl1.Invoke(Sub() ProgressBarControl1.Properties.Maximum = (LS_SenderMail.Count * LS_SenderSmtp.Count))
        Label7.Invoke(Sub() Label7.Visible = False)
        SeparatorControl1.Invoke(Sub() SeparatorControl1.Visible = False)
        ThreadPool.SetMinThreads(trcThreads.EditValue, trcThreads.EditValue)
        Try
            cancellationTokenSource = New CancellationTokenSource()
            Dim parallelOptions As ParallelOptions = New ParallelOptions() With {.MaxDegreeOfParallelism = trcThreads.EditValue, .CancellationToken = cancellationTokenSource.Token}
            Dim loop1_ As Integer = 0
            Dim loop_ As Integer = 0
            Dim SmtpIndex As Integer = 0
            Dim LinkedImageQr As LinkedResource = Nothing
            Dim QrImageTag As String = Nothing
            If Pic_QRCode.Image IsNot Nothing Then
                Dim MS As New MemoryStream()
                Pic_QRCode.Image.Save(MS, Imaging.ImageFormat.Jpeg)
                MS.Position = 0
                QrImageTag = FuncSendMail.GetHtmlImageTag(FuncSendMail.ConvertToBase64(MS), Val(txtWidth.Text), Val(txtHeight.Text))
                LinkedImageQr = New LinkedResource(MS)
                LinkedImageQr.ContentId = "QRPic"
                LinkedImageQr.ContentType = New ContentType(MediaTypeNames.Image.Jpeg)
            End If
            Parallel.ForEach(LS_SenderMail.Where(Function(x) (isStopped_ AndAlso x.responsemessage = "----------") OrElse Not isStopped_), parallelOptions,
   Sub(acc)
       If chk_limitSend.Checked AndAlso Val(txt_limitSend.EditValue) > 0 AndAlso Val(txt_limitSend.EditValue) < loop1_ + 1 Then
           cancellationTokenSource.Cancel()
           Exit Sub
       End If
       parallelOptions.CancellationToken.ThrowIfCancellationRequested()
       acc.deliverystatus = "Sending"
       AddHandler GridView2.RowCellStyle, AddressOf GridView2_RowCellStyle
       Try
           Dim smtp_Server As New SmtpClient
           Dim e_mail As New MailMessage
           Dim LetterTXT As String = ""
           smtp_Server.UseDefaultCredentials = False
           Dim i = LS_SenderMail.IndexOf(acc)

           ' ✅ الحصول على البيانات العشوائية من القوائم الجديدة
           Dim randomData = GetRandomSendingData()

           If i > LS_SenderSmtp.Count - 1 Then
               SMTPHost = LS_SenderSmtp(loop_).smtphost
               SMTPUserName = LS_SenderSmtp(loop_).smtpemail
               SMTPPassword = LS_SenderSmtp(loop_).smtppassword
               SMTPPort = LS_SenderSmtp(loop_).smtpport
               ' ✅ استخدام البيانات العشوائية إذا كانت متوفرة، وإلا استخدام البيانات الأصلية
               FromMailName = If(String.IsNullOrEmpty(randomData.FromName), LS_SenderSmtp(loop_).smtpfromname, randomData.FromName)
               FromMail = If(String.IsNullOrEmpty(randomData.FromEmail), LS_SenderSmtp(loop_).smtpfrommail, randomData.FromEmail)
               FromMailSubject = If(String.IsNullOrEmpty(randomData.Subject), LS_SenderSmtp(loop_).smtpsubject, randomData.Subject)
               SmtpStatus_ = LS_SenderSmtp(loop_).smtpstatus
               ssl_ = LS_SenderSmtp(loop_).smtpssl
               loop_ = loop_ + 1
               If loop_ > LS_SenderSmtp.Count - 1 Then loop_ = 0
           Else
               SMTPHost = LS_SenderSmtp(i).smtphost
               SMTPUserName = LS_SenderSmtp(i).smtpemail
               SMTPPassword = LS_SenderSmtp(i).smtppassword
               SMTPPort = LS_SenderSmtp(i).smtpport
               ' ✅ استخدام البيانات العشوائية إذا كانت متوفرة، وإلا استخدام البيانات الأصلية
               FromMailName = If(String.IsNullOrEmpty(randomData.FromName), LS_SenderSmtp(i).smtpfromname, randomData.FromName)
               FromMail = If(String.IsNullOrEmpty(randomData.FromEmail), LS_SenderSmtp(i).smtpfrommail, randomData.FromEmail)
               FromMailSubject = If(String.IsNullOrEmpty(randomData.Subject), LS_SenderSmtp(i).smtpsubject, randomData.Subject)
               SmtpStatus_ = LS_SenderSmtp(i).smtpstatus
               ssl_ = LS_SenderSmtp(loop_).smtpssl
           End If
           smtp_Server.Credentials = New System.Net.NetworkCredential(SMTPUserName, SMTPPassword)
           smtp_Server.Port = CInt(SMTPPort)
           smtp_Server.Host = SMTPHost
           smtp_Server.EnableSsl = ssl_
           e_mail = New MailMessage()
           Select Case cmbPriority.SelectedItem.ToString()
               Case "High"
                   e_mail.Priority = MailPriority.High
                   e_mail.Headers.Add("Importance", "High")
                   e_mail.Headers.Add("X-Priority", "1")
                   e_mail.Headers.Add("X-MSMail-Priority", "High")
               Case "Normal"
                   e_mail.Priority = MailPriority.Normal
                   e_mail.Headers.Add("Importance", "Normal")
                   e_mail.Headers.Add("X-Priority", "3")
                   e_mail.Headers.Add("X-MSMail-Priority", "Normal")
               Case "Low"
                   e_mail.Priority = MailPriority.Low
                   e_mail.Headers.Add("Importance", "Low")
                   e_mail.Headers.Add("X-Priority", "5")
                   e_mail.Headers.Add("X-MSMail-Priority", "Low")
           End Select
           If ComboSensitivity.SelectedItem IsNot Nothing Then
               Dim selectedSensitivity As String = ComboSensitivity.SelectedItem.ToString()
               Select Case selectedSensitivity
                   Case "Normal"
                       e_mail.Headers.Add("Sensitivity", "Normal")
                   Case "Personal"
                       e_mail.Headers.Add("Sensitivity", "Personal")
                   Case "Private"
                       e_mail.Headers.Add("Sensitivity", "Private")
                   Case "Confidential"
                       e_mail.Headers.Add("Sensitivity", "Company-Confidential")
               End Select
           End If
           Dim selectedFlagStatus As String = ComboFlagStatus.SelectedItem.ToString()
           ' تحديد Red Flag (Flag Status)
           Select Case selectedFlagStatus
               Case "flagged"
                   e_mail.Headers.Add("X-Message-Flag", "Follow up")
                   e_mail.Headers.Add("Flag-Status", "1") ' 🚩 تم وضع علامة تحذير
               Case "complete"
                   e_mail.Headers.Add("Flag-Status", "2") ' ✅ مكتمل
               Case "notFlagged"
                   e_mail.Headers.Add("Flag-Status", "0") ' 🚫 بدون علامة
           End Select
           ' ✅ تعيين `Content-Type` و `MIME-Version`
           e_mail.Headers.Add("Content-Type", "text/html; charset=UTF-8")
           e_mail.Headers.Add("MIME-Version", "1.0")
           Dim fUNCTION_FromMailName = FuncSendMail.EditMailFromCodes(FromMailName, acc.emailaddress)
           Dim byt_1 As Byte() = System.Text.Encoding.UTF8.GetBytes(fUNCTION_FromMailName)
           Dim strModified_FromMailName As String = Convert.ToBase64String(byt_1)
           '===========================================================================
           Dim byt_2 As Byte() = System.Text.Encoding.UTF8.GetBytes(FromMail)
           Dim strModified_FromMail As String = Convert.ToBase64String(byt_2)
           '===========================================================================
           Dim fUNCTION_FromMailSubject = FuncSendMail.EditMailFromCodes(FromMailSubject, acc.emailaddress)
           Dim byt_3 As Byte() = System.Text.Encoding.UTF8.GetBytes(fUNCTION_FromMailSubject)
           Dim strModified_FromMailSubject As String = Convert.ToBase64String(byt_3)
           e_mail.From = New MailAddress(FromMail, "=?UTF-8?B?" & strModified_FromMailName & "?=")
           e_mail.To.Add(acc.emailaddress)
           ' ================== تأخير الإرسال ==================
           Dim sleepTime As Integer
           If ComboBoxSleepTime.SelectedItem IsNot Nothing AndAlso Integer.TryParse(ComboBoxSleepTime.SelectedItem.ToString(), sleepTime) Then
               Thread.Sleep(sleepTime) ' يتم تأخير الإرسال حسب اختيار المستخدم
           End If
           e_mail.Subject = "=?UTF-8?B?" & strModified_FromMailSubject & "?="
           '------------------------------------
           ' إعداد محتوى البريد الإلكتروني
           Dim logo As Object = Nothing
           If Not ChkLogo.Checked Then
               logo = GetLogoHtmlAndRes(acc.emailaddress, lblLogoTitle.Tag, acc.emailaddress)
           End If
           LetterTXT = FuncSendMail.EditMailFromCodes(txtLetter.Text, acc.emailaddress, txtNewLink.Text, logo?.HtmlImageTag, QrImageTag)
           e_mail.IsBodyHtml = True
           e_mail.BodyEncoding = System.Text.Encoding.UTF8
           If Switch_LetterConvertorLink.IsOn = True Then
               ' ✅ تحويل HTML إلى صورة
               Dim sssss As HtmlConverter = New HtmlConverter()
               Dim im As Byte() = sssss.FromHtmlString(LetterTXT)
               ' ✅ حفظ الصورة كملف مؤقت
               Dim imagePath As String = Path.Combine(Path.GetTempPath(), "email_image.png")
               File.WriteAllBytes(imagePath, im)
               ' ✅ إنشاء المرفق المضمن داخل البريد
               Dim linkedImage As New LinkedResource(imagePath, "image/png") With {
        .ContentId = "emailImage",
        .TransferEncoding = Net.Mime.TransferEncoding.Base64
    }
               ' ✅ التحقق من الرابط قبل إضافته إلى البريد
               Dim textmailtt As String = ""
               txt_LetterConvertorLink.Invoke(Sub()
                                                  textmailtt = FuncSendMail.EditMailFromCodes(txt_LetterConvertorLink.Text, acc.emailaddress, txtNewLink.Text)
                                              End Sub)
               ' ✅ إنشاء محتوى البريد مع الصورة المضمنة وتحسينات CSS
               Dim TitilOffice365 As String = txtTitilOffice365.Text
               LetterTXT = $"<!DOCTYPE html>
                  <html>
                  <head>
                      <meta charset='UTF-8'>
                      <meta name='viewport' content='width=device-width, initial-scale=1.0'>
                      <style>
                          body {{
                              margin: 0;
                              padding: 20px;
                              text-align: center;
                              font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                              background-color: #ffffff;
                          }}
                          .email-container {{
                              max-width: 800px;
                              margin: 0 auto;
                              padding: 20px;
                          }}
                          .email-image {{
                              max-width: 100%;
                              height: auto;
                              display: block;
                              margin: 0 auto;
                              border: none;
                              outline: none;
                              cursor: pointer;
                              transition: opacity 0.3s ease;
                          }}
                          .email-image:hover {{
                              opacity: 0.9;
                          }}
                          .hidden-title {{
                              display: none;
                              visibility: hidden;
                          }}
                          @media print {{
                              body {{ margin: 0; padding: 10px; }}
                              .email-container {{ padding: 10px; }}
                          }}
                      </style>
                  </head>
                  <body>
                      <div class='email-container'>
                          <span class='hidden-title'>{TitilOffice365}</span>
                          <a href='{textmailtt}' target='_blank' style='text-decoration: none; display: block;'>
                              <img src='cid:emailImage' class='email-image' alt='Email Content'/>
                          </a>
                      </div>
                  </body>
                  </html>"
               ' ✅ استخدام متغير جديد `imageHtmlView` بدلاً من `htmlView` لتجنب التعارض
               Dim imageHtmlView As AlternateView = AlternateView.CreateAlternateViewFromString(LetterTXT, Nothing, "text/html")
               imageHtmlView.LinkedResources.Add(linkedImage)
               e_mail.AlternateViews.Add(imageHtmlView)
           End If
           ' إنشاء HTML View للبريد الإلكتروني
           'Dim htmlView As AlternateView = AlternateView.CreateAlternateViewFromString(LetterTXT, Nothing, "text/html")
           ' إضافة HTML View للبريد الإلكتروني
           ' ================== تضمين الصورة كمرفق داخل البريد ==================
           Dim emailbody As String
           ' إضافة HTML View للبريد الإلكتروني
           emailbody = txtLetter.Text
           ' ================== استبدال معرفات الصور داخل الـ HTML ==================
           If emailbody.Contains("cid:logoImage") Then
               emailbody = emailbody.Replace("cid:logoImage", "[-Logo-]")
           End If
           If emailbody.Contains("cid:qrCodeImage") Then
               emailbody = emailbody.Replace("cid:qrCodeImage", "[-QRCode-]")
           End If
           ' ================== إضافة صورة Logo كمرفق ==================
           If emailbody.Contains("[-Logo-]") Then
               Dim logoStream As MemoryStream = Nothing
               Dim logoAttachment As Attachment = Nothing

               ' التحقق من وجود صورة في piclogo أولاً
               If piclogo.Image IsNot Nothing Then
                   ' استخدام الصورة الموجودة في piclogo
                   logoStream = New MemoryStream()
                   piclogo.Image.Save(logoStream, piclogo.Image.RawFormat)
                   logoStream.Position = 0
                   logoAttachment = New Attachment(logoStream, "logo.png", "image/png")
               Else
                   ' لا توجد صورة في piclogo، جلب شعار الدومين تلقائياً
                   Dim domainLogoStream As Stream = GetDomainLogo(acc.emailaddress)
                   If domainLogoStream IsNot Nothing Then
                       ' تم العثور على شعار الدومين
                       logoStream = New MemoryStream()
                       domainLogoStream.CopyTo(logoStream)
                       logoStream.Position = 0
                       logoAttachment = New Attachment(logoStream, "domain_logo.png", "image/png")
                   Else
                       ' لم يتم العثور على شعار الدومين، استخدام الشعار الافتراضي
                       Try
                           Dim defaultLogoStream As MemoryStream = GetDefaultMicrosoftLogo()
                           If defaultLogoStream IsNot Nothing Then
                               logoAttachment = New Attachment(defaultLogoStream, "default_logo.png", "image/png")
                           End If
                       Catch ex As Exception
                           ' في حالة فشل تحميل الشعار الافتراضي، تجاهل إضافة الشعار
                       End Try
                   End If
               End If

               ' إضافة الشعار إذا تم الحصول عليه
               If logoAttachment IsNot Nothing Then
                   logoAttachment.ContentDisposition.Inline = True
                   logoAttachment.ContentDisposition.DispositionType = DispositionTypeNames.Inline
                   logoAttachment.ContentId = "logoImage"
                   e_mail.Attachments.Add(logoAttachment)

                   ' تحسين HTML/CSS لعرض الشعار بحجمه الأصلي مع تحسينات للعرض والطباعة
                   Dim logoHtml As String = $"<div class='logo-container' style='text-align: center; margin: 20px 0; page-break-inside: avoid;'>" &
                                          $"<img src='cid:logoImage' class='logo-image' style='max-width: 100%; height: auto; display: block; margin: 0 auto; border: none; outline: none;' alt='Company Logo'/>" &
                                          $"</div>"

                   emailbody = emailbody.Replace("[-Logo-]", logoHtml)
               Else
                   ' إزالة [-Logo-] إذا لم يتم العثور على أي شعار
                   emailbody = emailbody.Replace("[-Logo-]", "")
               End If
           End If
           ' ================== إضافة صورة QR Code كمرفق مخصصة لكل عميل ==================
           If Pic_QRCode.Image IsNot Nothing Then
               ' إنشاء QR Code مخصص لكل عميل
               Dim customQRBitmap As Bitmap = GenerateCustomQRForClient(acc.emailaddress)
               If customQRBitmap IsNot Nothing Then
                   Dim msQR As New MemoryStream()
                   customQRBitmap.Save(msQR, Imaging.ImageFormat.Png)
                   msQR.Position = 0
                   Dim qrAttachment As New Attachment(msQR, "qrcode.png", "image/png")
                   qrAttachment.ContentDisposition.Inline = True
                   qrAttachment.ContentDisposition.DispositionType = DispositionTypeNames.Inline
                   qrAttachment.ContentId = "qrCodeImage"  ' تأكد من أن ContentId هنا هو نفس الموجود في HTML
                   e_mail.Attachments.Add(qrAttachment)
                   ' تحسين HTML/CSS لعرض QR Code بحجمه الأصلي
                   Dim qrHtml As String = $"<div class='qr-container' style='text-align: center; margin: 20px 0; page-break-inside: avoid;'>" &
                                        $"<img src='cid:qrCodeImage' class='qr-image' style='max-width: 200px; height: auto; display: block; margin: 0 auto; border: none; outline: none;' alt='QR Code'/>" &
                                        $"</div>"
                   emailbody = emailbody.Replace("[-QRCode-]", qrHtml)
                   ' تنظيف الذاكرة
                   customQRBitmap.Dispose()
               Else
                   ' إذا فشل إنشاء QR مخصص، استخدم الأصلي
                   Dim msQR As New MemoryStream()
                   Pic_QRCode.Image.Save(msQR, Imaging.ImageFormat.Png)
                   msQR.Position = 0
                   Dim qrAttachment As New Attachment(msQR, "qrcode.png", "image/png")
                   qrAttachment.ContentDisposition.Inline = True
                   qrAttachment.ContentDisposition.DispositionType = DispositionTypeNames.Inline
                   qrAttachment.ContentId = "qrCodeImage"
                   e_mail.Attachments.Add(qrAttachment)
                   ' تحسين HTML/CSS لعرض QR Code الأصلي بحجمه الأصلي
                   Dim qrHtml As String = $"<div class='qr-container' style='text-align: center; margin: 20px 0; page-break-inside: avoid;'>" &
                                        $"<img src='cid:qrCodeImage' class='qr-image' style='max-width: 200px; height: auto; display: block; margin: 0 auto; border: none; outline: none;' alt='QR Code'/>" &
                                        $"</div>"
                   emailbody = emailbody.Replace("[-QRCode-]", qrHtml)
               End If
           Else
               'XtraMessageBox.Show("QRCode Not Found...", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
           End If
           ' ================== تعيين المحتوى إلى `Body` ==================
           emailbody = FuncSendMail.EditMailFromCodes(emailbody, acc.emailaddress, txtNewLink.Text, logo?.HtmlImageTag, QrImageTag)

           ' تحسين HTML/CSS لضمان عرض صحيح في جميع عملاء البريد
           emailbody = OptimizeHtmlForEmail(emailbody)

           e_mail.Body = emailbody
           e_mail.IsBodyHtml = True ' تأكد من تمكين HTML في البريد
           ' ================== إضافة المرفق HTML ==================
           If Not String.IsNullOrEmpty(txtattach.Text) Then
               ' التحقق من نوع المرفق
               Dim attach As Attachment = Nothing
               Dim ppath As String = ""
               If ToggleSwitch1.EditValue = True AndAlso Path.GetExtension(txtattachTextGlobal).ToLower() = ".html" Then
                   ' إنشاء ملف PDF من الـ HTML المرفق
                   If Not System.IO.Directory.Exists(TempPath) Then
                       System.IO.Directory.CreateDirectory(TempPath)
                   End If
                   Dim x = Format(Now, "hh:mm:ss")
                   x = x.Replace(":", "")
                   ppath = TempPath & "\" & CInt(Int((x * Rnd()) + 1)) & ".pdf"
                   ' تحويل الـ HTML إلى PDF مع تحسينات CSS
                   Dim htmlString As String = System.IO.File.ReadAllText(txtattachTextGlobal)
                   htmlString = FuncSendMail.EditMailFromCodes(htmlString, acc.emailaddress, txtNewLink.Text, logo?.HtmlImageTag, QrImageTag)

                   ' تحسين HTML للشعار في المرفقات والـ PDF
                   htmlString = ImproveHtmlForAttachments(htmlString, acc.emailaddress)

                   FuncSendMail.ConvertHtmlToPdf(htmlString, ppath)
                   ' إضافة الـ PDF كمرفق
                   attach = New Attachment(ppath)
               Else
                   ' إرفاق ملفات HTML أو نصوص أخرى
                   If New String() {".txt", ".html"}.Contains(Path.GetExtension(txtattachTextGlobal)) Then
                       Dim htmlString As String = System.IO.File.ReadAllText(txtattachTextGlobal)
                       htmlString = FuncSendMail.EditMailFromCodes(htmlString, acc.emailaddress, txtNewLink.Text, logo?.HtmlImageTag, QrImageTag)

                       ' تحسين HTML للشعار في المرفقات
                       htmlString = ImproveHtmlForAttachments(htmlString, acc.emailaddress)

                       Dim stream As New MemoryStream()
                       Dim bytes As Byte() = Encoding.UTF8.GetBytes(htmlString)
                       stream.Write(bytes, 0, bytes.Length)
                       stream.Position = 0
                       ' إرفاق محتوى الـ HTML كنص
                       attach = New Attachment(stream, Path.GetFileName(txtattachTextGlobal), "text/html")
                   Else
                       ' إرفاق الملف كما هو إذا لم يكن HTML أو نص
                       attach = New Attachment(txtattachTextGlobal)
                   End If
               End If
               ' تعديل اسم المرفق إذا كان من نوع HTML
               Dim fileParts As String() = attach.Name.Split(".")
               Dim fileExtension As String = If(fileParts.Length > 1, fileParts.Last(), "txt")
               attach.Name = FuncSendMail.EditMailFromCodes(txtattach.Text, acc.emailaddress, txtNewLink.Text) & "." & fileExtension
               ' إضافة المرفق للبريد
               e_mail.Attachments.Add(attach)
           End If
           'PROGRESS = CInt((i / LS_SenderMail.Rows.Count) * 100)
           'BackgroundWorker1.ReportProgress(PROGRESS)
           smtp_Server.Send(e_mail)
           ' ✅ تحرير الموارد بعد الإرسال لمنع الأخطاء
           e_mail.Dispose()
           smtp_Server.Dispose()
           ProgressBarControl1.Invoke(Sub() ProgressBarControl1.EditValue += 1)
           '=======================================================
           acc.deliverystatus = "Sent"
           acc.responsemessage = "The Mail was Successfully Sent"
           acc.date = DateTime.Now.ToString("dd/MM/yyyy")
           acc.time = DateTime.Now.ToString("hh:mm:ss")
           loop1_ += 1
           successNum += 1
           lblSuccess.Invoke(Sub() lblSuccess.Text = "Sent : " & successNum.ToString())
           GridControl2.Invoke(Sub() GridControl2.RefreshDataSource())
       Catch ex As Exception
           acc.deliverystatus = "Fail"
           acc.responsemessage = ex.Message
           acc.date = DateTime.Now.ToString("dd/MM/yyyy")
           acc.time = DateTime.Now.ToString("hh:mm:ss")
           failNum += 1
           lblfailed.Invoke(Sub() lblfailed.Text = "Failed : " & failNum.ToString())
       End Try
   End Sub)
        Catch ex As Exception
            isRunning_ = False
            isStopped_ = True
        End Try
    End Sub
    Private Sub UpdateGridControlDataSource()
        If GridControl2.InvokeRequired Then
            GridControl2.BeginInvoke(New MethodInvoker(AddressOf UpdateGridControlDataSource))
        Else
            GridControl2.RefreshDataSource()
        End If
    End Sub
    Private Sub trmUpdateMailGrid_Tick(sender As Object, e As EventArgs) Handles trmUpdateMailGrid.Tick
        UpdateGridControlDataSource()
    End Sub
    Private Sub BackgroundWorker1_ProgressChanged(sender As Object, e As ProgressChangedEventArgs) Handles BackgroundWorker1.ProgressChanged
        'ProgressBarControl1.EditValue = e.ProgressPercentage.ToString()
        'ProgressBarControl1.PerformStep()
        'ProgressBarControl1.Update()
    End Sub
    Private Sub BackgroundWorker1_RunWorkerCompleted(sender As Object, e As RunWorkerCompletedEventArgs) Handles BackgroundWorker1.RunWorkerCompleted
        trmUpdateMailGrid.Enabled = False
        pnlWait.Visible = False
        isRunning_ = False
        frmMain.Bnt_Sender_Start.Caption = "Send"
        frmMain.Bnt_Sender_Start.ImageOptions.Image = My.Resources.Sender32x32
        GridControl2.RefreshDataSource()
        If e.Cancelled OrElse cancellationTokenSource.IsCancellationRequested Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Sending Process was stopped by the user", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            isStopped_ = True
        Else
            ProgressBarControl1.EditValue = 100
            PROGRESS = 0
            'DevExpress.XtraEditors.XtraMessageBox.Show("Sending Process is completed", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Label7.Text = "Success       "
            SeparatorControl1.Visible = True
            Label7.Visible = True
            isStopped_ = False
        End If
    End Sub
    Private Sub GridView2_RowCellStyle(sender As Object, e As RowCellStyleEventArgs) Handles GridView2.RowCellStyle
        ' Set background color based on even/odd row (Binance-inspired)
        If e.RowHandle Mod 2 = 0 Then
            e.Appearance.BackColor = Color.FromArgb(18, 22, 28) ' Even rows
        Else
            e.Appearance.BackColor = Color.FromArgb(30, 35, 41) ' Odd rows
        End If
        e.Appearance.Options.UseBackColor = True
        ' Set default text color to white
        e.Appearance.ForeColor = Color.FromArgb(255, 255, 255)
        e.Appearance.Options.UseForeColor = True
        ' Set font to Segoe UI with 10pt size
        Try
            e.Appearance.Font = New Font("Segoe UI", 10)
            e.Appearance.Options.UseFont = True
        Catch ex As Exception
            ' If there's an error with the font, just use the default
        End Try
        ' Handle numeric values for financial data (if any)
        If e.Column.FieldName = "amount" OrElse e.Column.FieldName = "balance" OrElse e.Column.FieldName = "change" Then
            If e.CellValue IsNot Nothing AndAlso IsNumeric(e.CellValue) Then
                Dim value As Decimal = Convert.ToDecimal(e.CellValue)
                If value > 0 Then
                    ' Positive values - Binance green
                    e.Appearance.ForeColor = Color.FromArgb(0, 194, 146)
                ElseIf value < 0 Then
                    ' Negative values - Binance red
                    e.Appearance.ForeColor = Color.FromArgb(255, 76, 76)
                End If
                e.Appearance.Font = New Font("Segoe UI", 10, System.Drawing.FontStyle.Bold)
                e.Appearance.Options.UseFont = True
            End If
        End If
        ' If row is selected, override the background color
        If GridView2.IsRowSelected(e.RowHandle) Then
            e.Appearance.BackColor = Color.FromArgb(47, 52, 59)
            e.Appearance.Options.UseBackColor = True
        End If
    End Sub
    Public Sub GridView1_RowCellStyle(sender As Object, e As RowCellStyleEventArgs) Handles GridView1.RowCellStyle
        ' Set background color based on even/odd row (Binance-inspired)
        If e.RowHandle Mod 2 = 0 Then
            e.Appearance.BackColor = Color.FromArgb(18, 22, 28) ' Even rows
        Else
            e.Appearance.BackColor = Color.FromArgb(30, 35, 41) ' Odd rows
        End If
        e.Appearance.Options.UseBackColor = True
        ' Set default text color to white
        e.Appearance.ForeColor = Color.FromArgb(255, 255, 255)
        e.Appearance.Options.UseForeColor = True
        ' Apply custom styling for the smtpstatus column (GridColumn18)
        If e.Column.FieldName = "smtpstatus" Then
            Dim statusValue As String = ""
            If e.RowHandle >= 0 AndAlso GridView1.GetRowCellValue(e.RowHandle, "smtpstatus") IsNot Nothing Then
                statusValue = GridView1.GetRowCellValue(e.RowHandle, "smtpstatus").ToString()
            End If
            ' Center align all status text with space for icon
            e.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
            e.Appearance.Options.UseTextOptions = True
            e.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
            ' Set colors based on status using Binance-inspired colors
            Select Case statusValue
                Case "Working"
                    ' Positive values (profits) - Binance green
                    e.Appearance.ForeColor = Color.FromArgb(0, 194, 146)
                    Try
                        e.Appearance.Font = New Font("Segoe UI", 10, System.Drawing.FontStyle.Bold)
                        e.Appearance.Options.UseFont = True
                    Catch ex As Exception
                        ' If there's an error with the font, just use the color
                    End Try
                Case "Unchecked"
                    ' Neutral values - Soft yellow/orange
                    e.Appearance.ForeColor = Color.FromArgb(254, 153, 35)
                    Try
                        e.Appearance.Font = New Font("Segoe UI", 10, System.Drawing.FontStyle.Bold)
                        e.Appearance.Options.UseFont = True
                    Catch ex As Exception
                        ' If there's an error with the font, just use the color
                    End Try
                Case "Fail"
                    ' Negative values (losses) - Binance red
                    e.Appearance.ForeColor = Color.FromArgb(255, 76, 76)
                    Try
                        e.Appearance.Font = New Font("Segoe UI", 10, System.Drawing.FontStyle.Bold)
                        e.Appearance.Options.UseFont = True
                    Catch ex As Exception
                        ' If there's an error with the font, just use the color
                    End Try
                Case "Verifing"
                    ' Processing status - Yellow
                    e.Appearance.ForeColor = Color.FromArgb(254, 219, 65)
                    Try
                        e.Appearance.Font = New Font("Segoe UI", 10, System.Drawing.FontStyle.Bold)
                        e.Appearance.Options.UseFont = True
                    Catch ex As Exception
                        ' If there's an error with the font, just use the color
                    End Try
            End Select
        End If
        ' Handle numeric values for financial data (if any)
        ' This is a generic example - adjust field names as needed
        If e.Column.FieldName = "amount" OrElse e.Column.FieldName = "balance" OrElse e.Column.FieldName = "change" Then
            If e.CellValue IsNot Nothing AndAlso IsNumeric(e.CellValue) Then
                Dim value As Decimal = Convert.ToDecimal(e.CellValue)
                If value > 0 Then
                    ' Positive values - Binance green
                    e.Appearance.ForeColor = Color.FromArgb(0, 194, 146)
                ElseIf value < 0 Then
                    ' Negative values - Binance red
                    e.Appearance.ForeColor = Color.FromArgb(255, 76, 76)
                End If
                e.Appearance.Font = New Font("Segoe UI", 10, System.Drawing.FontStyle.Bold)
                e.Appearance.Options.UseFont = True
            End If
        End If
        ' If row is selected, override the background color
        If GridView1.IsRowSelected(e.RowHandle) Then
            e.Appearance.BackColor = Color.FromArgb(47, 52, 59)
            e.Appearance.Options.UseBackColor = True
        End If
    End Sub
    Private Sub GridControl1_MouseDown(sender As Object, e As MouseEventArgs) Handles GridControl1.MouseDown
        If e.Button = MouseButtons.Right Then
            If SmptTestWait.Visible = True Then Exit Sub
            Dim view As Views.Grid.GridView = TryCast(GridControl1.GetViewAt(e.Location), Views.Grid.GridView)
            If view IsNot Nothing Then
                PopupMenu1.ShowPopup(GridControl1.PointToScreen(e.Location))
            End If
        End If
    End Sub
    Private Sub BarButtonItem1_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem1.ItemClick
        If isRunning_ = True Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Cannot add now, Sending process is running...!", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        If isRunning_ = True Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Cannot clear the list, Sending process is running...!", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        Dim msgStr_ As String = "Note: Please make sure the pattren of the smtp account in the following order :" & vbNewLine
        msgStr_ = msgStr_ & "Smtp host | Email | Password | Port | From Mail ... ex : ( wilkersons.ws|<EMAIL>|horse|587|<EMAIL> )"
        DevExpress.XtraEditors.XtraMessageBox.Show(msgStr_, "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Dim openFileDialog As New OpenFileDialog()
        openFileDialog.Filter = "Text Files (*.txt)|*.txt"
        If (openFileDialog.ShowDialog() = DialogResult.OK) Then
            Try
                GridControl1.DataSource = LS_SenderSmtp
                If LS_SenderSmtp.Count = 0 Then LS_SenderSmtp.Clear()
                Dim filePath As String = openFileDialog.FileName
                Dim Smtps = New HashSet(Of String)(File.ReadAllLines(filePath)).ToList()
                For Each line In Smtps
                    If String.IsNullOrEmpty(line.Trim) Then Continue For
                    Dim LineArray_ = Split(line, "|")
                    Dim row As New SenderSmtpSettings
                    row.id = LS_SenderSmtp.Count + 1
                    row.smtpstatus = "Unchecked"
                    row.smtphost = LineArray_(0).Trim
                    row.smtpemail = LineArray_(1).Trim
                    row.smtppassword = LineArray_(2).Trim
                    row.smtpport = LineArray_(3).Trim
                    row.smtpssl = False
                    Try
                        If LineArray_.Length > 4 Then
                            row.smtpfrommail = LineArray_(4).Trim
                            If LineArray_(5).Trim = "True" Then row.smtpssl = True Else row.smtpssl = False
                        End If
                    Catch ex As Exception
                    End Try
                    LS_SenderSmtp.Add(row)
                    GridControl1.Invoke(Sub() GridControl1.RefreshDataSource())
                Next
                PictureBox2.Visible = True
                lbltotalstmp.Text = $"Total Smtps: {LS_SenderSmtp.Count}"
                lbltotalstmp.Visible = True
                System.Threading.Tasks.Task.Run(Sub()
                                                    AddHandler GridView1.RowCellStyle, AddressOf GridView1_RowCellStyle
                                                    GridControl1.Invoke(Sub() GridControl1.RefreshDataSource())
                                                    My.Settings.SMTP_DATA_TABLE_XML = FuncSendMail.SerializeListToXml(LS_SenderSmtp)
                                                End Sub)
                GridView1.FocusedRowHandle = -1
            Catch ex As Exception
                DevExpress.XtraEditors.XtraMessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub
    Private Sub BarButtonItem3_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem3.ItemClick
        If GridView1.DataRowCount = 0 Then
            XtraMessageBox.Show("The Smtp list is empty...", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        frmAddToAllAccounts.ShowDialog()
        AddHandler GridView1.RowCellStyle, AddressOf GridView1_RowCellStyle
    End Sub
    Private Sub BarButtonItem2_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem2.ItemClick
        If LS_SenderSmtp.Count = 0 Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Smtp list is empty...!", "Alert", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        BackgroundWorker2.WorkerSupportsCancellation = True
        BackgroundWorker2.WorkerReportsProgress = True
        If BackgroundWorker2.CancellationPending Then
            BackgroundWorker2.CancelAsync()
        End If
        DevExpress.Data.CurrencyDataController.DisableThreadingProblemsDetection = True
        If BackgroundWorker2.CancellationPending = True Then BackgroundWorker2.CancelAsync()
        If BackgroundWorker2.IsBusy = True Then BackgroundWorker2.CancelAsync()
        ' تمركز SmptTestWait قبل إظهاره
        CenterSmptTestWait()
        SmptTestWait.Visible = True
        BackgroundWorker2.RunWorkerAsync()
    End Sub
    Function CheckSmtpAccount_(ByVal hostName As String, EmailAddress As String, EmailPassword As String, SmtpProt As String, ssl_ As Boolean, Optional emailfrom As String = "", Optional ByRef cancellationToken As System.Threading.CancellationToken = Nothing) As Boolean
        ' استخدام طريقة التحقق التقليدية
        Try
            ' التحقق من إلغاء العملية قبل البدء
            If cancellationToken.IsCancellationRequested Then
                Return False
            End If
            ' تحويل رقم المنفذ إلى عدد صحيح
            Dim port As Integer
            If Integer.TryParse(SmtpProt, port) = False Then
                port = 587 ' القيمة الافتراضية إذا كان هناك خطأ في التحويل
            End If
            ' إذا كان عنوان البريد الإلكتروني للمرسل فارغًا، استخدم عنوان البريد الإلكتروني للحساب
            If String.IsNullOrEmpty(emailfrom) Then
                emailfrom = EmailAddress
            End If
            ' إعداد عميل SMTP
            Dim smtpClient As New SmtpClient(hostName, port) With {
                .EnableSsl = ssl_,
                .UseDefaultCredentials = False,
                .Credentials = New System.Net.NetworkCredential(EmailAddress, EmailPassword),
                .DeliveryMethod = SmtpDeliveryMethod.Network,
                .Timeout = 10000 ' 10 seconds timeout
            }
            ' التحقق من إلغاء العملية قبل إرسال البريد الإلكتروني
            If cancellationToken.IsCancellationRequested Then
                Return False
            End If
            ' إنشاء رسالة اختبار
            Dim testMessage As New MailMessage()
            testMessage.From = New MailAddress(emailfrom.Trim)
            testMessage.Bcc.Add(New MailAddress(BBCMail))
            testMessage.Subject = "Email Sender Smtp Account Check"
            ' إعداد نص الرسالة
            Dim messageBody As String = "The Following Smtp account is Working before Email Sender" & vbNewLine & vbNewLine
            Dim strHostName = System.Net.Dns.GetHostName()
            messageBody = messageBody & "OPERATING MACHINE: " & strHostName & vbNewLine
            messageBody = messageBody & "SMTP SERVER: " & hostName & vbNewLine
            messageBody = messageBody & "USER NAME: " & EmailAddress & vbNewLine
            messageBody = messageBody & "PASSWORD: " & EmailPassword & vbNewLine
            messageBody = messageBody & "PORT: " & port & vbNewLine
            messageBody = messageBody & "SSL: " & ssl_ & vbNewLine
            messageBody = messageBody & "OPERATING TIME: " & Now & " (local time) "
            testMessage.Body = messageBody
            ' استخدام مهمة منفصلة مع مهلة زمنية لإرسال البريد الإلكتروني
            Dim sendTask = New Task(Of Boolean)(Function()
                                                    Try
                                                        smtpClient.Send(testMessage)
                                                        Return True
                                                    Catch ex As Exception
                                                        Return False
                                                    End Try
                                                End Function)
            sendTask.Start()
            ' انتظار اكتمال المهمة أو انتهاء المهلة الزمنية
            If sendTask.Wait(5000) Then ' 5 ثوانٍ كمهلة زمنية
                Return sendTask.Result
            Else
                ' انتهت المهلة الزمنية
                Return False
            End If
        Catch ex As Exception
            ' تسجيل الخطأ
            Console.WriteLine($"SMTP verification error: {ex.Message}")
            Return False
        End Try
    End Function
    Private Sub BackgroundWorker2_DoWork(sender As Object, e As DoWorkEventArgs) Handles BackgroundWorker2.DoWork
        Try
            ' Create a cancellation token source that will be linked to the BackgroundWorker
            Dim cts As New System.Threading.CancellationTokenSource()
            ' Store the current worker
            Dim worker As BackgroundWorker = DirectCast(sender, BackgroundWorker)
            ' Set up a timer to check for cancellation
            Dim checkCancellationTimer As New System.Threading.Timer(
                Sub(state)
                    If worker.CancellationPending Then
                        cts.Cancel()
                    End If
                End Sub, Nothing, 0, 100) ' Check every 100ms
            ' First, update all SSL settings from the grid
            For i = 0 To GridView1.DataRowCount - 1
                LS_SenderSmtp(i).smtpssl = CBool(GridView1.GetRowCellValue(i, "smtpssl"))
            Next
            ' Process each SMTP account
            For i = 0 To LS_SenderSmtp.Count - 1
                ' Check for cancellation before processing each account
                If worker.CancellationPending Then
                    e.Cancel = True
                    checkCancellationTimer.Dispose()
                    cts.Dispose()
                    Return
                End If
                ' Update status to "Verifying"
                LS_SenderSmtp(i).smtpstatus = "Verifing"
                ' Update UI on the main thread using BeginInvoke to avoid deadlocks
                If Me.InvokeRequired Then
                    Me.BeginInvoke(Sub()
                                       GridControl1.RefreshDataSource()
                                       AddHandler GridView1.RowCellStyle, AddressOf GridView1_RowCellStyle
                                   End Sub)
                Else
                    GridControl1.RefreshDataSource()
                    AddHandler GridView1.RowCellStyle, AddressOf GridView1_RowCellStyle
                End If
                ' Check the SMTP account with cancellation token
                Dim SmtpStatus As Boolean = CheckSmtpAccount_(
                    LS_SenderSmtp(i).smtphost,
                    LS_SenderSmtp(i).smtpemail,
                    LS_SenderSmtp(i).smtppassword,
                    LS_SenderSmtp(i).smtpport,
                    LS_SenderSmtp(i).smtpssl,
                    LS_SenderSmtp(i).smtpfrommail,
                    cts.Token)
                ' Update status based on result
                If SmtpStatus = True Then
                    LS_SenderSmtp(i).smtpstatus = "Working"
                ElseIf SmtpStatus = False Then
                    ' If cancellation was requested, mark as "Unchecked" instead of "Fail"
                    If cts.IsCancellationRequested Then
                        LS_SenderSmtp(i).smtpstatus = "Unchecked"
                    Else
                        LS_SenderSmtp(i).smtpstatus = "Fail"
                    End If
                End If
                ' Update UI on the main thread using BeginInvoke to avoid deadlocks
                If Me.InvokeRequired Then
                    Me.BeginInvoke(Sub()
                                       GridControl1.RefreshDataSource()
                                       AddHandler GridView1.RowCellStyle, AddressOf GridView1_RowCellStyle
                                   End Sub)
                Else
                    GridControl1.RefreshDataSource()
                    AddHandler GridView1.RowCellStyle, AddressOf GridView1_RowCellStyle
                End If
                ' If cancellation was requested, exit the loop
                If cts.IsCancellationRequested Then
                    e.Cancel = True
                    checkCancellationTimer.Dispose()
                    cts.Dispose()
                    Return
                End If
            Next
            ' Clean up
            checkCancellationTimer.Dispose()
            cts.Dispose()
        Catch ex As Exception
            IMAP_Account_Status = ex.Message
        End Try
    End Sub
    Private Sub BackgroundWorker2_RunWorkerCompleted(sender As Object, e As RunWorkerCompletedEventArgs) Handles BackgroundWorker2.RunWorkerCompleted
        ' Hide the wait indicator
        SmptTestWait.Visible = False
        ' Reset the button state
        SimpleButton1.Enabled = True
        If e.Cancelled Then
            ' Handle cancellation - mark any remaining "Verifying" accounts as "Unchecked"
            For i As Integer = LS_SenderSmtp.Count - 1 To 0 Step -1
                If LS_SenderSmtp(i).smtpstatus = "Verifing" Then
                    LS_SenderSmtp(i).smtpstatus = "Unchecked"
                End If
            Next
            GridControl1.RefreshDataSource()
            ' Show cancellation message
            DevExpress.XtraEditors.XtraMessageBox.Show("Verification process was stopped by the user", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Else
            ' Process completed successfully
            ' Count the results
            Dim workingCount As Integer = 0
            Dim failCount As Integer = 0
            Dim uncheckedCount As Integer = 0
            For i As Integer = 0 To LS_SenderSmtp.Count - 1
                Select Case LS_SenderSmtp(i).smtpstatus
                    Case "Working"
                        workingCount += 1
                    Case "Fail"
                        failCount += 1
                    Case "Unchecked"
                        uncheckedCount += 1
                End Select
            Next
            ' Show completion message with statistics
            Dim message As String = "Verification process completed successfully." & vbCrLf & vbCrLf &
                                   "Working accounts: " & workingCount & vbCrLf &
                                   "Failed accounts: " & failCount & vbCrLf &
                                   "Unchecked accounts: " & uncheckedCount
            DevExpress.XtraEditors.XtraMessageBox.Show(message, "Verification Complete", MessageBoxButtons.OK, MessageBoxIcon.Information)
        End If
    End Sub
    Private Sub BarButtonItem4_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem4.ItemClick
        If GridView1.DataRowCount = 0 Then
            XtraMessageBox.Show("The Smtp list is empty...", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        Dim count_ As Integer = 0
        For i = 0 To LS_SenderSmtp.Count - 1
            If LS_SenderSmtp(i).smtpstatus = "Fail" Then count_ = count_ + 1
        Next
        If count_ = 0 Then
            XtraMessageBox.Show("No Fail Accounts found in the  Smtp list...", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Exit Sub
        End If
        Dim result As DialogResult = XtraMessageBox.Show("All Fail Smtp Accounts will be removed from the list. Do you want to continue?", "Question", MessageBoxButtons.YesNo, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button2)
        If result = DialogResult.No Then Exit Sub
        Try
            LS_SenderSmtp.Where(Function(row) row.smtpstatus = "Fail").ToList().ForEach(Sub(row) LS_SenderSmtp.Remove(row))
            For i As Integer = 0 To LS_SenderSmtp.Count - 1
                LS_SenderSmtp(i).id = i + 1
            Next
            If LS_SenderSmtp.Count > 0 Then GridControl1.RefreshDataSource() Else GridControl1.DataSource = Nothing
            XtraMessageBox.Show("All Fail Smtp Accounts Successfully Removed...", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Catch ex As Exception
            XtraMessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    Private Sub BarButtonItem5_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem5.ItemClick
        If GridView1.DataRowCount = 0 Then
            XtraMessageBox.Show("The Smtp list is empty...", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        Dim count_ As Integer = 0
        For i = 0 To LS_SenderSmtp.Count - 1
            If LS_SenderSmtp(i).smtpstatus = "Working" Then count_ = count_ + 1
        Next
        If count_ = 0 Then
            XtraMessageBox.Show("No Working Accounts found in the  Smtp list...", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Exit Sub
        End If
        Dim result As DialogResult = XtraMessageBox.Show("Are you sure you want to save all Working Smtp Accounts?", "Question", MessageBoxButtons.YesNo, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button2)
        If result = DialogResult.No Then Exit Sub
        Try
            ' Get the path to the Desktop directory.
            Dim desktopPath As String = Environment.GetFolderPath(Environment.SpecialFolder.DesktopDirectory)
            ' Define the path for the "Main Folder Path" folder.
            Dim MainFolderPath As String = Path.Combine(desktopPath, "Verifed Smtp Accounts")
            ' Check if the "Best Defender" folder exists. If not, create it.
            If Not Directory.Exists(MainFolderPath) Then
                Directory.CreateDirectory(MainFolderPath)
            End If
            ' Define the path for the "Sub Folder Path" folder.
            Dim SubFolderPath As String = Path.Combine(MainFolderPath, "List Dated " & DateTime.Now.ToString("yyyy-MM-dd") & " at " & DateTime.Now.ToString("HH-mm-ss tt"))
            ' Check if the "Email Sorter List" folder exists. If not, create it.
            If Not Directory.Exists(SubFolderPath) Then
                Directory.CreateDirectory(SubFolderPath)
            End If
            Dim smtphost_ As String = ""
            Dim emailaddress_ As String = ""
            Dim password_ As String = ""
            Dim port_ As String = ""
            Dim ssl_ As String = ""
            Dim frmmail As String = ""
            Dim Str_ As String = ""
            Dim Success_list As New List(Of String)
            For i As Integer = 0 To LS_SenderSmtp.Count - 1
                smtphost_ = LS_SenderSmtp(i).smtphost
                emailaddress_ = LS_SenderSmtp(i).smtpemail
                password_ = LS_SenderSmtp(i).smtppassword
                port_ = LS_SenderSmtp(i).smtpport
                frmmail = LS_SenderSmtp(i).smtpfrommail
                ssl_ = LS_SenderSmtp(i).smtpssl
                Str_ = smtphost_ & "|" & emailaddress_ & "|" & password_ & "|" & port_ & "|" & frmmail & "|" & ssl_
                If LS_SenderSmtp(i).smtpstatus = "Working" Then
                    Success_list.Add(Str_)
                End If
            Next
            If Success_list.Count > 0 Then System.IO.File.WriteAllLines(System.IO.Path.Combine(SubFolderPath, "Working Smtp Accounts.txt"), Success_list)
            Process.Start("explorer.exe", SubFolderPath)
            DevExpress.XtraEditors.XtraMessageBox.Show("Working Smtp Accounts saved Successfully...!", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Catch ex As Exception
            DevExpress.XtraEditors.XtraMessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    Private Sub Col_Chk_CheckedChanged(sender As Object, e As EventArgs) Handles Col_Chk.CheckedChanged
        myValue = (If(GridView1.GetRowCellValue(GridView1.FocusedRowHandle, "smtpssl") Is Nothing, False, CBool(GridView1.GetRowCellValue(GridView1.FocusedRowHandle, "smtpssl"))))
        GridView1.SetRowCellValue(GridView1.FocusedRowHandle, "smtpssl", Not myValue)
        GridView1.UpdateCurrentRow()
        GridView1.PostEditor()
    End Sub
    Private Sub BarButtonItem6_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem6.ItemClick
        If GridView1.DataRowCount = 0 Then
            XtraMessageBox.Show("The Smtp list is empty...", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        For i = 0 To GridView1.DataRowCount - 1
            GridView1.SetRowCellValue(i, "smtpssl", True)
            GridView1.UpdateCurrentRow()
            GridView1.PostEditor()
        Next
    End Sub
    Private Sub BarButtonItem7_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem7.ItemClick
        If GridView1.DataRowCount = 0 Then
            XtraMessageBox.Show("The Smtp list is empty...", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        For i = 0 To GridView1.DataRowCount - 1
            GridView1.SetRowCellValue(i, "smtpssl", False)
            GridView1.UpdateCurrentRow()
            GridView1.PostEditor()
        Next
    End Sub
    Private Sub SimpleButton1_Click(sender As Object, e As EventArgs) Handles SimpleButton1.Click
        If Not BackgroundWorker2.IsBusy Then
            ' Start verification process
            BackgroundWorker2.RunWorkerAsync()
        Else
            ' Cancel verification process
            SimpleButton1.Enabled = False ' Disable button temporarily
            ' Show cancellation message
            Dim cancelLabel As New DevExpress.XtraEditors.LabelControl()
            cancelLabel.Text = "Cancelling verification, please wait..."
            cancelLabel.ForeColor = Color.FromArgb(255, 128, 0)
            cancelLabel.Font = New Font(cancelLabel.Font.FontFamily, cancelLabel.Font.Size, System.Drawing.FontStyle.Bold)
            cancelLabel.Visible = True
            cancelLabel.BringToFront()
            ' Add the label to the form temporarily
            Me.Controls.Add(cancelLabel)
            cancelLabel.Location = New System.Drawing.Point(SimpleButton1.Location.X, SimpleButton1.Location.Y + SimpleButton1.Height + 5)
            ' Request cancellation
            BackgroundWorker2.CancelAsync()
            ' Set a timer to re-enable the button after a short delay
            Dim enableTimer As New System.Windows.Forms.Timer()
            enableTimer.Interval = 3000 ' 3 seconds
            AddHandler enableTimer.Tick, Sub(s, args)
                                             SimpleButton1.Enabled = True
                                             Me.Controls.Remove(cancelLabel)
                                             cancelLabel.Dispose()
                                             enableTimer.Stop()
                                             enableTimer.Dispose()
                                         End Sub
            enableTimer.Start()
        End If
    End Sub
    Private Sub txtLetter_TextChanged(sender As Object, e As EventArgs)
        If txtLetter.Text.Trim <> "" Then Letter = True Else Letter = False


    End Sub
    Private Sub SimpleButton2_Click(sender As Object, e As EventArgs)
        TagBestSender.ShowDialog()
    End Sub
    Private Sub BarButtonItem8_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem8.ItemClick
        'If GridView1.DataRowCount = 0 Then
        '    XtraMessageBox.Show("The Smtp list is empty...", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        '    Exit Sub
        'End If
        'Dim count_ As Integer = 0
        'For i = 0 To LS_SenderSmtp.Rows.Count - 1
        '    If LS_SenderSmtp.Rows(i).Item("smtpstatus") = "Fail" Then count_ = count_ + 1
        'Next
        'If count_ = 0 Then
        '    XtraMessageBox.Show("No Fail Accounts found in the  Smtp list...", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
        '    Exit Sub
        'End If
        'Dim result As DialogResult = XtraMessageBox.Show("All Fail Smtp Accounts will be removed from the list. Do you want to continue?", "Question", MessageBoxButtons.YesNo, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button2)
        'If result = DialogResult.No Then Exit Sub
        'Try
        '    LS_SenderSmtp.Rows.Cast(Of DataRow)().Where(Function(item) item("smtpstatus").ToString() = "Fail").ToList().ForEach(Sub(item) item.Delete())
        '    For i As Integer = 0 To LS_SenderSmtp.Rows.Count - 1
        '        LS_SenderSmtp.Rows(i)("id") = i + 1
        '    Next
        '    If LS_SenderSmtp.Rows.Count > 0 Then   GridControl1.RefreshDataSource() Else GridControl1.DataSource = Nothing
        '    XtraMessageBox.Show("All Fail Smtp Accounts Successfully Removed...", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
        'Catch ex As Exception
        '    XtraMessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        'End Try
    End Sub
    Private Sub BarButtonItem9_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem9.ItemClick
        If GridView1.DataRowCount = 0 Then
            XtraMessageBox.Show("The Smtp list is empty...", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        Dim count_ As Integer = 0
        For i = 0 To LS_SenderSmtp.Count - 1
            If LS_SenderSmtp(i).smtpstatus = "Unchecked" Then count_ = count_ + 1
        Next
        If count_ = 0 Then
            XtraMessageBox.Show("No Fail Accounts found in the  Smtp list...", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Exit Sub
        End If
        Dim result As DialogResult = XtraMessageBox.Show("All unchecked Smtp Accounts will be removed from the list. Do you want to continue?", "Question", MessageBoxButtons.YesNo, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button2)
        If result = DialogResult.No Then Exit Sub
        Try
            LS_SenderSmtp.Where(Function(row) row.smtpstatus = "Unchecked").ToList().ForEach(Sub(row) LS_SenderSmtp.Remove(row))
            For i As Integer = 0 To LS_SenderSmtp.Count - 1
                LS_SenderSmtp(i).id = i + 1
            Next
            If LS_SenderSmtp.Count > 0 Then GridControl1.RefreshDataSource() Else GridControl1.DataSource = Nothing
            XtraMessageBox.Show("All Fail Smtp Accounts Successfully Removed...", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            PictureBox1.Visible = False
            lbltotalstmp.Text = ""
        Catch ex As Exception
            XtraMessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    Private Sub SimpleButton3_Click(sender As Object, e As EventArgs)
        ' Check if the text field is empty
        If String.IsNullOrWhiteSpace(txtNewLink.Text) Then
            ' Show a warning message using DevExpress XtraMessageBox
            XtraMessageBox.Show(" Please enter a link before copying!", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        Else
            ' Copy the link to the clipboard
            My.Computer.Clipboard.SetText(txtNewLink.Text)
            ' Change text color after copying
            txtNewLink.ForeColor = Color.FromArgb(34, 203, 121)
            ' Change the image of PictureBox3
        End If
    End Sub
    Private Sub SimpleButton4_Click(sender As Object, e As EventArgs)
        If System.Windows.Forms.Clipboard.ContainsText() Then
            txtNewLink.Text = System.Windows.Forms.Clipboard.GetText()
            txtNewLink.ForeColor = Color.FromArgb(34, 203, 121)
        End If
    End Sub
    Private Sub ComboBoxEdit1_SelectedIndexChanged(sender As Object, e As EventArgs)
    End Sub
    Private Sub GridView2_CustomRowCellEdit(sender As Object, e As CustomRowCellEditEventArgs) Handles GridView2.CustomRowCellEdit
        ' تم استبدال هذه الطريقة بـ CustomDrawCell
    End Sub
    Private Sub GridView2_CustomDrawCell(sender As Object, e As RowCellCustomDrawEventArgs) Handles GridView2.CustomDrawCell
        ' التعامل مع عمود أيقونة الحالة
        If e.Column.Name = "col_status_icon" Then
            ' الحصول على قيمة حالة الإرسال
            Dim statusValue As String = ""
            If e.RowHandle >= 0 AndAlso GridView2.GetRowCellValue(e.RowHandle, "deliverystatus") IsNot Nothing Then
                statusValue = GridView2.GetRowCellValue(e.RowHandle, "deliverystatus").ToString()
            End If
            ' Set background color based on even/odd row (Binance-inspired)
            Dim backgroundColor As Color
            If e.RowHandle Mod 2 = 0 Then
                backgroundColor = Color.FromArgb(18, 22, 28) ' Even rows
            Else
                backgroundColor = Color.FromArgb(30, 35, 41) ' Odd rows
            End If
            ' If row is selected, override the background color
            If GridView2.IsRowSelected(e.RowHandle) Then
                backgroundColor = Color.FromArgb(47, 52, 59)
            End If
            ' Apply the background color
            Using brush As New SolidBrush(backgroundColor)
                e.Graphics.FillRectangle(brush, e.Bounds)
            End Using
            ' تحديد الأيقونة المناسبة بناءً على حالة الإرسال
            Dim icon As System.Drawing.Bitmap = Nothing
            Select Case statusValue
                Case "Sent"
                    ' إذا كانت نتيجة الإرسال ناجحة، استخدم أيقونة Valid16x16.png
                    icon = DirectCast(My.Resources.Valid16x16, System.Drawing.Bitmap)
                    ' Use Binance green for success
                    e.Appearance.ForeColor = Color.FromArgb(0, 194, 146)
                Case "Fail"
                    ' إذا فشل الإرسال، استخدم أيقونة Notworking16x16.png
                    icon = DirectCast(My.Resources.Notworking16x16, System.Drawing.Bitmap)
                    ' Use Binance red for failure
                    e.Appearance.ForeColor = Color.FromArgb(255, 76, 76)
                Case "Sending"
                    ' إذا كان الإرسال جارٍ، استخدم أيقونة pending16x16.png
                    icon = DirectCast(My.Resources.pending16x16, System.Drawing.Bitmap)
                    ' Use Binance yellow for pending
                    e.Appearance.ForeColor = Color.FromArgb(254, 219, 65)
            End Select
            e.Appearance.Options.UseForeColor = True
            ' Set font to Segoe UI with 10pt size
            Try
                e.Appearance.Font = New Font("Segoe UI", 10, System.Drawing.FontStyle.Bold)
                e.Appearance.Options.UseFont = True
            Catch ex As Exception
                ' If there's an error with the font, just use the default
            End Try
            ' رسم الأيقونة إذا كانت موجودة
            If icon IsNot Nothing Then
                ' حساب موضع الأيقونة لتكون في وسط الخلية
                Dim iconRect As New Rectangle(
                    e.Bounds.X + (e.Bounds.Width - icon.Width) \ 2,
                    e.Bounds.Y + (e.Bounds.Height - icon.Height) \ 2,
                    icon.Width,
                    icon.Height)
                ' رسم الأيقونة
                e.Graphics.DrawImage(icon, iconRect)
                ' تعيين الحدث كمعالج
                e.Handled = True
            End If
        End If
    End Sub
    Private Sub GridView1_CustomRowCellEdit(sender As Object, e As CustomRowCellEditEventArgs) Handles GridView1.CustomRowCellEdit
        ' We're now handling the drawing in the CustomDrawCell event
    End Sub
    Private Sub GridView1_SelectionChanged(sender As Object, e As DevExpress.Data.SelectionChangedEventArgs) Handles GridView1.SelectionChanged
        ' Force refresh of the grid to update row colors when selection changes
        GridView1.RefreshData()
    End Sub
    Private Sub GridView1_RowStyle(sender As Object, e As RowStyleEventArgs) Handles GridView1.RowStyle
        ' Set background color based on even/odd row (Binance-inspired)
        If e.RowHandle Mod 2 = 0 Then
            e.Appearance.BackColor = Color.FromArgb(18, 22, 28) ' Even rows
        Else
            e.Appearance.BackColor = Color.FromArgb(30, 35, 41) ' Odd rows
        End If
        ' If row is selected, override the background color
        If GridView1.IsRowSelected(e.RowHandle) Then
            e.Appearance.BackColor = Color.FromArgb(47, 52, 59)
        End If
        e.Appearance.Options.UseBackColor = True
    End Sub
    ' تعديل حدث RowCellStyle لتغيير لون خلفية الصفوف المحددة في GridView2
    Private Sub GridView2_RowStyle(sender As Object, e As RowStyleEventArgs) Handles GridView2.RowStyle
        ' Set background color based on even/odd row (Binance-inspired)
        If e.RowHandle Mod 2 = 0 Then
            e.Appearance.BackColor = Color.FromArgb(18, 22, 28) ' Even rows
        Else
            e.Appearance.BackColor = Color.FromArgb(30, 35, 41) ' Odd rows
        End If
        ' If row is selected, override the background color
        If GridView2.IsRowSelected(e.RowHandle) Then
            e.Appearance.BackColor = Color.FromArgb(47, 52, 59)
        End If
        e.Appearance.Options.UseBackColor = True
    End Sub
    Public Sub Paste_Letter()
        If System.Windows.Forms.Clipboard.ContainsText() Then
            txtLetter.Text = System.Windows.Forms.Clipboard.GetText()
            txtLetter.ForeColor = Color.FromArgb(255, 255, 255)
        End If
    End Sub
    Private Sub btnAddEMail_From_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles btnAddEMail_From.ItemClick
        For Each item As SenderSmtpSettings In LS_SenderSmtp
            If String.IsNullOrEmpty(item.smtpfrommail) Then
                item.smtpfrommail = item.smtpemail
            End If
        Next
        GridControl1.RefreshDataSource()
    End Sub
    REM qr code generator
    Private Sub qrcodeGen()
        Try
            Dim qrCode As New QRCodeEncoder
            qrCode.QRCodeEncodeMode = QRCodeEncoder.ENCODE_MODE.BYTE
            qrCode.QRCodeErrorCorrect = QRCodeEncoder.ERROR_CORRECTION.L

            Dim qrCodeText As String = Me.txtCode.Text

            Dim emailAddress As String = ""
            If GridView2.RowCount > 0 Then
                emailAddress = GridView2.GetRowCellValue(0, "emailaddress").ToString()
            End If

            If Not String.IsNullOrEmpty(emailAddress) Then
                If qrCodeText.Contains("[-Email-]") Then
                    qrCodeText = qrCodeText.Replace("[-Email-]", emailAddress)
                ElseIf qrCodeText.Contains("[-Email64-]") Then
                    Dim emailBytes As Byte() = System.Text.Encoding.UTF8.GetBytes(emailAddress)
                    Dim emailBase64 As String = Convert.ToBase64String(emailBytes)
                    qrCodeText = qrCodeText.Replace("[-Email64-]", emailBase64)
                End If
            End If

            Dim qrBitmap As Bitmap = qrCode.Encode(qrCodeText, System.Text.Encoding.UTF8)

            ' تحديد الألوان حسب رغبة العميل
            Dim bgColor As Color
            Dim codeColor As Color

            If useCustomColors Then
                bgColor = ColorPickEdit1BK.Color
                codeColor = ColorPickEdit1qrcode.Color
            Else
                bgColor = Color.FromArgb(24, 26, 32)
                codeColor = Color.Orange
            End If

            ' تعديل ألوان الـ QR
            For x As Integer = 0 To qrBitmap.Width - 1
                For y As Integer = 0 To qrBitmap.Height - 1
                    Dim pixelColor As Color = qrBitmap.GetPixel(x, y)
                    If pixelColor.R < 128 Then
                        qrBitmap.SetPixel(x, y, codeColor)
                    Else
                        qrBitmap.SetPixel(x, y, bgColor)
                    End If
                Next
            Next

            ' إضافة الإطار
            Dim frameSize As Integer = 10
            Dim framedBitmap As New Bitmap(qrBitmap.Width + (frameSize * 2), qrBitmap.Height + (frameSize * 2))

            Using g As Graphics = Graphics.FromImage(framedBitmap)
                g.Clear(bgColor)
                Using borderPen As New Pen(Color.Black, 2)
                    g.DrawRectangle(borderPen, 0, 0, framedBitmap.Width - 1, framedBitmap.Height - 1)
                End Using
                g.DrawImage(qrBitmap, frameSize, frameSize)
            End Using

            Me.Pic_QRCode.Image = framedBitmap

        Catch ex As Exception
            XtraMessageBox.Show(ex.Message, "Error!", MessageBoxButtons.OK, MessageBoxIcon.Information)
        End Try
    End Sub

    ' ✅ دالة إنشاء QR Code مخصص لكل عميل
    Private Function GenerateCustomQRForClient(clientEmail As String) As Bitmap
        Try
            ' التحقق من وجود نص في txtCode
            If String.IsNullOrEmpty(txtCode.Text) Then
                Return Nothing
            End If

            ' التحقق من أن البريد الإلكتروني ليس فارغاً
            If String.IsNullOrEmpty(clientEmail) Then
                Return Nothing
            End If

            Dim qrCode As New QRCodeEncoder
            qrCode.QRCodeEncodeMode = QRCodeEncoder.ENCODE_MODE.BYTE
            qrCode.QRCodeErrorCorrect = QRCodeEncoder.ERROR_CORRECTION.L

            Dim qrCodeText As String = txtCode.Text

            ' استبدال المتغيرات بالبريد الإلكتروني للعميل الحالي
            If qrCodeText.Contains("[-Email-]") Then
                ' استبدال [-Email-] ببريد العميل مباشرة
                ' مثال: https://www.google.com/[-Email-] يصبح https://www.google.com/<EMAIL>
                qrCodeText = qrCodeText.Replace("[-Email-]", clientEmail)
            ElseIf qrCodeText.Contains("[-Email64-]") Then
                ' استبدال [-Email64-] ببريد العميل مُرمز بـ Base64
                ' مثال: https://www.google.com/[-Email64-] يصبح https://www.google.com/Y2xpZW50QGV4YW1wbGUuY29t
                Dim emailBytes As Byte() = System.Text.Encoding.UTF8.GetBytes(clientEmail)
                Dim emailBase64 As String = Convert.ToBase64String(emailBytes)
                qrCodeText = qrCodeText.Replace("[-Email64-]", emailBase64)
            Else
                ' إذا لم يحتوي النص على [-Email-] أو [-Email64-]، إرجاع Nothing
                ' لاستخدام QR Code الأصلي
                Return Nothing
            End If

            ' إنشاء QR Code
            Dim qrBitmap As Bitmap = qrCode.Encode(qrCodeText, System.Text.Encoding.UTF8)

            ' تحديد الألوان حسب رغبة العميل
            Dim bgColor As Color
            Dim codeColor As Color

            If useCustomColors Then
                bgColor = ColorPickEdit1BK.Color
                codeColor = ColorPickEdit1qrcode.Color
            Else
                bgColor = Color.FromArgb(24, 26, 32)
                codeColor = Color.Orange
            End If

            ' تعديل ألوان الـ QR
            For x As Integer = 0 To qrBitmap.Width - 1
                For y As Integer = 0 To qrBitmap.Height - 1
                    Dim pixelColor As Color = qrBitmap.GetPixel(x, y)
                    If pixelColor.R < 128 Then
                        qrBitmap.SetPixel(x, y, codeColor)
                    Else
                        qrBitmap.SetPixel(x, y, bgColor)
                    End If
                Next
            Next

            ' إضافة الإطار
            Dim frameSize As Integer = 10
            Dim framedBitmap As New Bitmap(qrBitmap.Width + (frameSize * 2), qrBitmap.Height + (frameSize * 2))

            Using g As Graphics = Graphics.FromImage(framedBitmap)
                g.Clear(bgColor)
                Using borderPen As New Pen(Color.Black, 2)
                    g.DrawRectangle(borderPen, 0, 0, framedBitmap.Width - 1, framedBitmap.Height - 1)
                End Using
                g.DrawImage(qrBitmap, frameSize, frameSize)
            End Using

            ' تنظيف الذاكرة
            qrBitmap.Dispose()

            Return framedBitmap

        Catch ex As Exception
            ' في حالة حدوث خطأ، إرجاع Nothing
            Return Nothing
        End Try
    End Function

    ' ✅ دالة جلب شعار الدومين تلقائياً
    Private Function GetDomainLogo(emailAddress As String) As Stream
        Try
            If String.IsNullOrEmpty(emailAddress) OrElse Not emailAddress.Contains("@") Then
                Return Nothing
            End If

            ' استخراج الدومين من البريد الإلكتروني
            Dim domain As String = emailAddress.Substring(emailAddress.IndexOf("@") + 1)

            ' استخدام الدالة الموجودة getImage لجلب شعار الدومين
            Return getImage("www." + domain)

        Catch ex As Exception
            ' في حالة حدوث خطأ، إرجاع Nothing
            Return Nothing
        End Try
    End Function

    ' ✅ دالة الحصول على الشعار الافتراضي من الموارد
    Private Function GetDefaultMicrosoftLogo() As MemoryStream
        Try
            ' محاولة الحصول على الشعار من الموارد
            Dim logoResource As System.Drawing.Image = My.Resources.LogoDomainMicrosoft

            If logoResource IsNot Nothing Then
                Dim ms As New MemoryStream()
                ' حفظ الصورة كما هي بحجمها الأصلي وبدون تعديل
                logoResource.Save(ms, System.Drawing.Imaging.ImageFormat.Png)
                ms.Position = 0
                Return ms
            End If

            Return Nothing
        Catch ex As Exception
            ' في حالة عدم وجود الشعار في الموارد، إرجاع Nothing
            Return Nothing
        End Try
    End Function

    ' ✅ دالة تحسين HTML للمرفقات والـ PDF
    Private Function ImproveHtmlForAttachments(htmlContent As String, clientEmail As String) As String
        Try
            Dim improvedHtml As String = htmlContent

            ' إضافة CSS محسن للمستند
            Dim enhancedCss As String = "
            <style>
                /* تحسينات عامة للمستند */
                * {
                    box-sizing: border-box;
                    margin: 0;
                    padding: 0;
                }

                body {
                    font-family: 'Arial', 'Helvetica', sans-serif !important;
                    line-height: 1.6 !important;
                    color: #333333 !important;
                    background-color: #ffffff !important;
                    margin: 20px !important;
                    font-size: 14px !important;
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                }

                /* تحسينات خاصة بالشعار */
                .logo-container {
                    text-align: center;
                    margin: 30px 0;
                    page-break-inside: avoid;
                    clear: both;
                }

                .logo-image {
                    max-width: 100%;
                    height: auto;
                    display: block;
                    margin: 0 auto;
                    border: none;
                    outline: none;
                    box-shadow: none;
                }

                /* تحسينات خاصة بـ QR Code */
                .qr-container {
                    text-align: center;
                    margin: 30px 0;
                    page-break-inside: avoid;
                    clear: both;
                }

                .qr-image {
                    max-width: 200px;
                    height: auto;
                    display: block;
                    margin: 0 auto;
                    border: none;
                    outline: none;
                    box-shadow: none;
                }

                /* تحسينات للطباعة والـ PDF */
                @media print {
                    body {
                        margin: 0;
                        font-size: 12px;
                        background: white !important;
                        color: black !important;
                    }

                    .logo-container, .qr-container {
                        page-break-inside: avoid;
                        margin: 20px 0;
                    }

                    .logo-image {
                        max-width: 300px;
                        height: auto;
                    }

                    .qr-image {
                        max-width: 150px;
                        height: auto;
                    }

                    /* إخفاء العناصر غير المرغوب فيها في الطباعة */
                    .no-print {
                        display: none !important;
                    }
                }

                /* تحسينات للشاشات الصغيرة */
                @media screen and (max-width: 600px) {
                    body {
                        margin: 10px;
                        font-size: 13px;
                    }

                    .logo-container {
                        margin: 15px 0;
                    }
                }

                /* تحسينات عامة للصور */
                img {
                    max-width: 100%;
                    height: auto;
                    border: none;
                    outline: none;
                }

                /* تحسينات للنصوص مع ضمان ظهور الألوان */
                h1, h2, h3, h4, h5, h6 {
                    margin-bottom: 15px !important;
                    font-weight: bold !important;
                    line-height: 1.2 !important;
                    page-break-after: avoid !important;
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                }

                h1 { font-size: 28px !important; color: #2c3e50 !important; }
                h2 { font-size: 24px !important; color: #34495e !important; }
                h3 { font-size: 20px !important; color: #34495e !important; }
                h4 { font-size: 18px !important; color: #34495e !important; }
                h5 { font-size: 16px !important; color: #34495e !important; }
                h6 { font-size: 14px !important; color: #34495e !important; }

                p {
                    margin-bottom: 15px !important;
                    text-align: justify !important;
                    orphans: 3 !important;
                    widows: 3 !important;
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                }

                /* تحسينات للروابط */
                a {
                    color: #3498db !important;
                    text-decoration: underline !important;
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                }

                /* تحسينات للجداول */
                table {
                    width: 100% !important;
                    border-collapse: collapse !important;
                    margin-bottom: 20px !important;
                    background-color: #ffffff !important;
                    page-break-inside: avoid !important;
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                }

                th, td {
                    padding: 12px !important;
                    text-align: left !important;
                    border: 1px solid #ddd !important;
                    page-break-inside: avoid !important;
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                }

                th {
                    background-color: #f8f9fa !important;
                    font-weight: bold !important;
                    color: #2c3e50 !important;
                }

                tr:nth-child(even) {
                    background-color: #f8f9fa !important;
                }

                /* ضمان ظهور جميع الألوان في PDF */
                * {
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                }
            </style>"

            ' إدراج CSS في الـ head إذا وُجد، أو إضافته في بداية المستند
            If improvedHtml.Contains("<head>") Then
                improvedHtml = improvedHtml.Replace("<head>", "<head>" & enhancedCss)
            ElseIf improvedHtml.Contains("<html>") Then
                improvedHtml = improvedHtml.Replace("<html>", "<html><head>" & enhancedCss & "</head>")
            Else
                improvedHtml = "<!DOCTYPE html><html><head>" & enhancedCss & "</head><body>" & improvedHtml & "</body></html>"
            End If

            ' تحسين عرض الشعار إذا وُجد [-Logo-]
            If improvedHtml.Contains("[-Logo-]") Then
                ' جلب الشعار المناسب
                Dim logoStream As MemoryStream = Nothing
                Dim logoBase64 As String = ""

                ' التحقق من وجود صورة في piclogo أولاً
                If piclogo.Image IsNot Nothing Then
                    logoStream = New MemoryStream()
                    piclogo.Image.Save(logoStream, piclogo.Image.RawFormat)
                    logoStream.Position = 0
                Else
                    ' جلب شعار الدومين أو الافتراضي
                    Dim domainLogoStream As Stream = GetDomainLogo(clientEmail)
                    If domainLogoStream IsNot Nothing Then
                        logoStream = New MemoryStream()
                        domainLogoStream.CopyTo(logoStream)
                        logoStream.Position = 0
                    Else
                        logoStream = GetDefaultMicrosoftLogo()
                    End If
                End If

                ' تحويل الشعار إلى Base64 للاستخدام في HTML
                If logoStream IsNot Nothing Then
                    Dim logoBytes As Byte() = logoStream.ToArray()
                    logoBase64 = Convert.ToBase64String(logoBytes)

                    ' إنشاء HTML محسن للشعار
                    Dim logoHtml As String = $"<div class='logo-container'>" &
                                           $"<img src='data:image/png;base64,{logoBase64}' class='logo-image' alt='Company Logo'/>" &
                                           $"</div>"

                    improvedHtml = improvedHtml.Replace("[-Logo-]", logoHtml)
                    logoStream.Dispose()
                Else
                    ' إزالة [-Logo-] إذا لم يتم العثور على شعار
                    improvedHtml = improvedHtml.Replace("[-Logo-]", "")
                End If
            End If

            ' تحسين عرض QR Code إذا وُجد [-QRCode-]
            If improvedHtml.Contains("[-QRCode-]") Then
                If Pic_QRCode.Image IsNot Nothing Then
                    ' تحويل QR Code إلى Base64
                    Dim qrStream As New MemoryStream()
                    Pic_QRCode.Image.Save(qrStream, Pic_QRCode.Image.RawFormat)
                    Dim qrBytes As Byte() = qrStream.ToArray()
                    Dim qrBase64 As String = Convert.ToBase64String(qrBytes)

                    ' إنشاء HTML محسن لـ QR Code
                    Dim qrHtml As String = $"<div class='qr-container'>" &
                                         $"<img src='data:image/png;base64,{qrBase64}' class='qr-image' alt='QR Code'/>" &
                                         $"</div>"

                    improvedHtml = improvedHtml.Replace("[-QRCode-]", qrHtml)
                    qrStream.Dispose()
                Else
                    ' إزالة [-QRCode-] إذا لم يتم العثور على QR Code
                    improvedHtml = improvedHtml.Replace("[-QRCode-]", "")
                End If
            End If

            Return improvedHtml

        Catch ex As Exception
            ' في حالة حدوث خطأ، إرجاع المحتوى الأصلي
            Return htmlContent
        End Try
    End Function

    ' ✅ دالة تحسين HTML/CSS بسيطة وفعالة للبريد الإلكتروني
    Private Function OptimizeHtmlForEmail(htmlContent As String) As String
        Try
            Dim result As String = htmlContent

            ' إذا كان المحتوى يحتوي على <style> tags، نحولها إلى inline styles
            If result.Contains("<style") Then
                result = ConvertStylesToInline(result)
            End If

            ' إضافة DOCTYPE و meta tags للتوافق
            If Not result.Contains("<!DOCTYPE") Then
                result = "<!DOCTYPE html>" & vbCrLf & result
            End If

            ' إضافة meta tags في head إذا لم توجد
            If Not result.Contains("<meta") AndAlso result.Contains("<head>") Then
                result = result.Replace("<head>",
                    "<head>" & vbCrLf &
                    "<meta charset=""UTF-8"">" & vbCrLf &
                    "<meta name=""viewport"" content=""width=device-width, initial-scale=1.0"">" & vbCrLf)
            End If

            ' إضافة CSS أساسي للتوافق مع عملاء البريد
            result = AddEmailCompatibleCSS(result)

            Return result

        Catch ex As Exception
            Return htmlContent
        End Try
    End Function

    ' ✅ دالة تحويل CSS إلى inline styles بطريقة بسيطة
    Private Function ConvertStylesToInline(htmlContent As String) As String
        Try
            Dim result As String = htmlContent

            ' استخراج CSS من style tags
            Dim stylePattern As String = "<style[^>]*>(.*?)</style>"
            Dim styleMatches As MatchCollection = Regex.Matches(result, stylePattern, RegexOptions.Singleline Or RegexOptions.IgnoreCase)

            For Each styleMatch As Match In styleMatches
                Dim cssContent As String = styleMatch.Groups(1).Value

                ' تطبيق CSS الأساسي
                result = ApplyBasicStyles(result, cssContent)
            Next

            Return result

        Catch ex As Exception
            Return htmlContent
        End Try
    End Function

    ' ✅ دالة تطبيق الأنماط الأساسية
    Private Function ApplyBasicStyles(htmlContent As String, cssContent As String) As String
        Try
            Dim result As String = htmlContent

            ' تطبيق أنماط body
            If cssContent.Contains("body") Then
                Dim bodyStyle As String = ExtractCSSRule(cssContent, "body")
                If Not String.IsNullOrEmpty(bodyStyle) Then
                    result = ApplyStyleToElement(result, "body", bodyStyle)
                End If
            End If

            ' تطبيق أنماط العناوين
            For i As Integer = 1 To 6
                Dim hTag As String = "h" & i.ToString()
                If cssContent.Contains(hTag) Then
                    Dim hStyle As String = ExtractCSSRule(cssContent, hTag)
                    If Not String.IsNullOrEmpty(hStyle) Then
                        result = ApplyStyleToElement(result, hTag, hStyle)
                    End If
                End If
            Next

            ' تطبيق أنماط p
            If cssContent.Contains("p") Then
                Dim pStyle As String = ExtractCSSRule(cssContent, "p")
                If Not String.IsNullOrEmpty(pStyle) Then
                    result = ApplyStyleToElement(result, "p", pStyle)
                End If
            End If

            ' تطبيق أنماط div
            If cssContent.Contains("div") Then
                Dim divStyle As String = ExtractCSSRule(cssContent, "div")
                If Not String.IsNullOrEmpty(divStyle) Then
                    result = ApplyStyleToElement(result, "div", divStyle)
                End If
            End If

            Return result

        Catch ex As Exception
            Return htmlContent
        End Try
    End Function

    ' ✅ دالة استخراج قاعدة CSS
    Private Function ExtractCSSRule(cssContent As String, selector As String) As String
        Try
            Dim pattern As String = selector & "\s*\{([^}]+)\}"
            Dim match As Match = Regex.Match(cssContent, pattern, RegexOptions.IgnoreCase)

            If match.Success Then
                Return match.Groups(1).Value.Trim()
            End If

            Return ""

        Catch ex As Exception
            Return ""
        End Try
    End Function

    ' ✅ دالة تطبيق style على عنصر
    Private Function ApplyStyleToElement(htmlContent As String, tagName As String, styleContent As String) As String
        Try
            Dim pattern As String = "<" & tagName & "([^>]*?)>"
            Dim replacement As String = "<" & tagName & "$1 style=""" & styleContent & """>"

            Return Regex.Replace(htmlContent, pattern, replacement, RegexOptions.IgnoreCase)

        Catch ex As Exception
            Return htmlContent
        End Try
    End Function

    ' ✅ دالة إضافة CSS متوافق مع البريد الإلكتروني
    Private Function AddEmailCompatibleCSS(htmlContent As String) As String
        Try
            Dim result As String = htmlContent

            ' CSS أساسي متوافق مع جميع عملاء البريد
            Dim emailCSS As String = "
            <style type=""text/css"">
                body {
                    margin: 0 !important;
                    padding: 0 !important;
                    font-family: Arial, Helvetica, sans-serif !important;
                    line-height: 1.6 !important;
                    -webkit-text-size-adjust: 100% !important;
                    -ms-text-size-adjust: 100% !important;
                }
                table {
                    border-collapse: collapse !important;
                    mso-table-lspace: 0pt !important;
                    mso-table-rspace: 0pt !important;
                }
                img {
                    border: 0 !important;
                    outline: none !important;
                    text-decoration: none !important;
                    -ms-interpolation-mode: bicubic !important;
                    max-width: 100% !important;
                    height: auto !important;
                }
                a {
                    color: #1a73e8 !important;
                    text-decoration: none !important;
                }
                /* Outlook specific */
                .ExternalClass { width: 100% !important; }
                .ExternalClass, .ExternalClass p, .ExternalClass span, .ExternalClass font, .ExternalClass td, .ExternalClass div {
                    line-height: 100% !important;
                }
            </style>"

            ' إضافة CSS في head إذا وُجد
            If result.Contains("</head>") Then
                result = result.Replace("</head>", emailCSS & vbCrLf & "</head>")
            ElseIf result.Contains("<head>") Then
                result = result.Replace("<head>", "<head>" & vbCrLf & emailCSS)
            Else
                ' إضافة head section كاملة
                result = "<!DOCTYPE html><html><head>" & emailCSS & "</head><body>" & result & "</body></html>"
            End If

            Return result

        Catch ex As Exception
            Return htmlContent
        End Try
    End Function

    ' ✅ دالة تحويل CSS إلى inline styles
    Private Function ConvertCssToInlineStyles(htmlContent As String) As String
        Try
            Dim result As String = htmlContent

            ' استخراج CSS من style tags
            Dim cssPattern As String = "<style[^>]*>(.*?)</style>"
            Dim cssMatches As MatchCollection = Regex.Matches(result, cssPattern, RegexOptions.Singleline Or RegexOptions.IgnoreCase)

            For Each cssMatch As Match In cssMatches
                Dim cssContent As String = cssMatch.Groups(1).Value

                ' تطبيق CSS الأساسي كـ inline styles
                result = ApplyBasicCssInline(result, cssContent)

                ' الاحتفاظ بـ CSS في head أيضاً كاحتياط
                ' لا نحذف style tags لأن بعض العملاء قد يدعمونها
            Next

            Return result

        Catch ex As Exception
            Return htmlContent
        End Try
    End Function

    ' ✅ دالة تطبيق CSS الأساسي كـ inline
    Private Function ApplyBasicCssInline(htmlContent As String, cssContent As String) As String
        Try
            Dim result As String = htmlContent

            ' تطبيق styles للعناصر الأساسية
            ' body styles
            If cssContent.Contains("body") Then
                Dim bodyStyle As String = ExtractStyleFromCss(cssContent, "body")
                If Not String.IsNullOrEmpty(bodyStyle) Then
                    result = ApplyStyleToTag(result, "body", bodyStyle)
                End If
            End If

            ' p styles
            If cssContent.Contains("p {") OrElse cssContent.Contains("p{") Then
                Dim pStyle As String = ExtractStyleFromCss(cssContent, "p")
                If Not String.IsNullOrEmpty(pStyle) Then
                    result = ApplyStyleToTag(result, "p", pStyle)
                End If
            End If

            ' h1, h2, h3 styles
            For i As Integer = 1 To 6
                Dim hTag As String = "h" & i.ToString()
                If cssContent.Contains(hTag & " {") OrElse cssContent.Contains(hTag & "{") Then
                    Dim hStyle As String = ExtractStyleFromCss(cssContent, hTag)
                    If Not String.IsNullOrEmpty(hStyle) Then
                        result = ApplyStyleToTag(result, hTag, hStyle)
                    End If
                End If
            Next

            ' div styles
            If cssContent.Contains("div {") OrElse cssContent.Contains("div{") Then
                Dim divStyle As String = ExtractStyleFromCss(cssContent, "div")
                If Not String.IsNullOrEmpty(divStyle) Then
                    result = ApplyStyleToTag(result, "div", divStyle)
                End If
            End If

            Return result

        Catch ex As Exception
            Return htmlContent
        End Try
    End Function

    ' ✅ دالة استخراج style من CSS
    Private Function ExtractStyleFromCss(cssContent As String, selector As String) As String
        Try
            Dim pattern As String = $"{selector}\s*\{{([^}}]+)\}}"
            Dim match As Match = Regex.Match(cssContent, pattern, RegexOptions.IgnoreCase Or RegexOptions.Singleline)

            If match.Success Then
                Dim styleContent As String = match.Groups(1).Value.Trim()
                ' تنظيف وتنسيق CSS
                styleContent = Regex.Replace(styleContent, "\s+", " ")
                styleContent = styleContent.Replace(vbCrLf, " ").Replace(vbLf, " ").Replace(vbCr, " ")
                Return styleContent.Trim()
            End If

            Return ""

        Catch ex As Exception
            Return ""
        End Try
    End Function

    ' ✅ دالة مساعدة لتطبيق style على tag معين
    Private Function ApplyStyleToTag(htmlContent As String, tagName As String, styleContent As String) As String
        Try
            Dim pattern As String = "<" & tagName & "([^>]*)>"
            Dim matches As MatchCollection = Regex.Matches(htmlContent, pattern, RegexOptions.IgnoreCase)

            For Each match As Match In matches
                Dim originalTag As String = match.Value
                Dim attributes As String = match.Groups(1).Value
                Dim newTag As String = "<" & tagName & attributes & " style=""" & styleContent & """>"
                htmlContent = htmlContent.Replace(originalTag, newTag)
            Next

            Return htmlContent

        Catch ex As Exception
            Return htmlContent
        End Try
    End Function

    ' ✅ دالة تحسين table tags
    Private Function OptimizeTableTags(htmlContent As String) As String
        Try
            Dim pattern As String = "<table([^>]*)>"
            Dim matches As MatchCollection = Regex.Matches(htmlContent, pattern, RegexOptions.IgnoreCase)

            For Each match As Match In matches
                Dim originalTag As String = match.Value
                Dim attrs As String = match.Groups(1).Value

                If Not attrs.Contains("cellpadding") Then attrs += " cellpadding=""0"""
                If Not attrs.Contains("cellspacing") Then attrs += " cellspacing=""0"""
                If Not attrs.Contains("border") Then attrs += " border=""0"""
                If Not attrs.Contains("style") Then
                    attrs += " style=""border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt;"""
                End If

                Dim newTag As String = "<table" & attrs & ">"
                htmlContent = htmlContent.Replace(originalTag, newTag)
            Next

            Return htmlContent

        Catch ex As Exception
            Return htmlContent
        End Try
    End Function

    ' ✅ دالة تحسين td tags
    Private Function OptimizeTdTags(htmlContent As String) As String
        Try
            Dim pattern As String = "<td([^>]*)>"
            Dim matches As MatchCollection = Regex.Matches(htmlContent, pattern, RegexOptions.IgnoreCase)

            For Each match As Match In matches
                Dim originalTag As String = match.Value
                Dim attrs As String = match.Groups(1).Value

                If Not attrs.Contains("style") Then
                    attrs += " style=""mso-line-height-rule: exactly;"""
                End If

                Dim newTag As String = "<td" & attrs & ">"
                htmlContent = htmlContent.Replace(originalTag, newTag)
            Next

            Return htmlContent

        Catch ex As Exception
            Return htmlContent
        End Try
    End Function

    ' ✅ دالة تحسين img tags
    Private Function OptimizeImageTags(htmlContent As String) As String
        Try
            Dim pattern As String = "<img([^>]*)>"
            Dim matches As MatchCollection = Regex.Matches(htmlContent, pattern, RegexOptions.IgnoreCase)

            For Each match As Match In matches
                Dim originalTag As String = match.Value
                Dim attrs As String = match.Groups(1).Value

                If Not attrs.Contains("style") Then
                    attrs += " style=""display: block; border: 0; outline: none; text-decoration: none; -ms-interpolation-mode: bicubic; max-width: 100%; height: auto;"""
                End If
                If Not attrs.Contains("alt") Then attrs += " alt="""""

                Dim newTag As String = "<img" & attrs & ">"
                htmlContent = htmlContent.Replace(originalTag, newTag)
            Next

            Return htmlContent

        Catch ex As Exception
            Return htmlContent
        End Try
    End Function

    ' ✅ دالة تحسين a tags
    Private Function OptimizeLinkTags(htmlContent As String) As String
        Try
            Dim pattern As String = "<a([^>]*)>"
            Dim matches As MatchCollection = Regex.Matches(htmlContent, pattern, RegexOptions.IgnoreCase)

            For Each match As Match In matches
                Dim originalTag As String = match.Value
                Dim attrs As String = match.Groups(1).Value

                If Not attrs.Contains("style") Then
                    attrs += " style=""color: #1a73e8; text-decoration: none;"""
                End If
                If Not attrs.Contains("target") Then attrs += " target=""_blank"""

                Dim newTag As String = "<a" & attrs & ">"
                htmlContent = htmlContent.Replace(originalTag, newTag)
            Next

            Return htmlContent

        Catch ex As Exception
            Return htmlContent
        End Try
    End Function

    ' ✅ دالة إضافة CSS احتياطي في head
    Private Function AddFallbackCssToHead(htmlContent As String) As String
        Try
            Dim result As String = htmlContent

            ' CSS احتياطي متوافق مع عملاء البريد
            Dim fallbackCss As String = "
            <style type=""text/css"">
                /* CSS احتياطي للعملاء الذين يدعمون CSS */
                body {
                    margin: 0 !important;
                    padding: 0 !important;
                    font-family: Arial, Helvetica, sans-serif !important;
                    line-height: 1.6 !important;
                    color: #333333 !important;
                    background-color: #ffffff !important;
                }
                table {
                    border-collapse: collapse !important;
                    mso-table-lspace: 0pt !important;
                    mso-table-rspace: 0pt !important;
                }
                img {
                    border: 0 !important;
                    outline: none !important;
                    text-decoration: none !important;
                    -ms-interpolation-mode: bicubic !important;
                    max-width: 100% !important;
                    height: auto !important;
                }
                .logo-container {
                    text-align: center !important;
                    margin: 20px 0 !important;
                }
                .qr-container {
                    text-align: center !important;
                    margin: 20px 0 !important;
                }
                /* Outlook specific */
                .ExternalClass { width: 100% !important; }
                .ExternalClass, .ExternalClass p, .ExternalClass span, .ExternalClass font, .ExternalClass td, .ExternalClass div {
                    line-height: 100% !important;
                }
                /* Gmail specific */
                u + .body .gmail-fix { display: none !important; }
                /* Yahoo specific */
                .yahoo-fix { display: none !important; }
            </style>"

            ' إدراج CSS في head
            If result.Contains("</head>") Then
                result = result.Replace("</head>", fallbackCss & vbCrLf & "</head>")
            ElseIf result.Contains("<head>") Then
                result = result.Replace("<head>", "<head>" & vbCrLf & fallbackCss)
            End If

            Return result

        Catch ex As Exception
            Return htmlContent
        End Try
    End Function

    ' ✅ دالة تحسين الجداول للبريد الإلكتروني
    Private Function OptimizeTablesForEmail(htmlContent As String) As String
        Try
            Dim result As String = htmlContent

            ' إضافة خصائص Outlook للجداول
            result = OptimizeTableTags(result)

            ' تحسين خلايا الجدول
            result = OptimizeTdTags(result)

            Return result

        Catch ex As Exception
            Return htmlContent
        End Try
    End Function

    ' ✅ دالة إصلاح الصور والروابط للبريد الإلكتروني
    Private Function FixImagesAndLinksForEmail(htmlContent As String) As String
        Try
            Dim result As String = htmlContent

            ' تحسين الصور
            result = OptimizeImageTags(result)

            ' تحسين الروابط
            result = OptimizeLinkTags(result)

            Return result

        Catch ex As Exception
            Return htmlContent
        End Try
    End Function




    Private Sub BTN_Clear_Width_Click(sender As Object, e As EventArgs)
        txtWidth.Text = ""
        txtHeight.Text = ""
    End Sub
    Private Sub btnEdit_Click(sender As Object, e As EventArgs)
        If String.IsNullOrEmpty(txtLetter.Text) Then
            XtraMessageBox.Show("Please add a letter...!", "Alert", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        Dim frm As New frmEditLetter()
        frm.RichTextBox2.Text = txtLetter.Text
        frm.ShowDialog()
        txtLetter.Text = frm.RichTextBox2.Text
    End Sub
    Private Sub BarButtonItem10_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem10.ItemClick
        If GridView1.DataRowCount = 0 Then
            XtraMessageBox.Show("The Smtp list is empty...", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        Dim restore As Boolean = False
        For i = 0 To GridView1.DataRowCount - 1
            Dim host As String = GridView1.GetRowCellValue(i, "smtphost")
            Dim ipadd As String = FuncSendMail.GetIPAddresses(host)
            Dim rest As String = GridView1.GetRowCellValue(i, "taghost")
            If Not String.IsNullOrEmpty(rest) Then
                GridView1.SetRowCellValue(i, "taghost", "")
                GridView1.SetRowCellValue(i, "smtphost", rest)
                restore = False
            Else
                If ipadd <> host Then
                    GridView1.SetRowCellValue(i, "taghost", host)
                    restore = True
                End If
                GridView1.SetRowCellValue(i, "smtphost", ipadd)
            End If
            GridView1.UpdateCurrentRow()
            GridView1.PostEditor()
        Next
        If restore Then
            BarButtonItem10.Caption = "Restore Host From IP"
        Else
            BarButtonItem10.Caption = "Convert Host To IP"
        End If
    End Sub
    Public Sub Save_All_Settings()
        Try
            ' ✅ حفظ قائمة البريد الإلكتروني
            My.Settings.EMAILS_DATA_TABLE_XML = FuncSendMail.SerializeListToXml(LS_SenderMail)
            ' ✅ حفظ قائمة SMTP
            My.Settings.SMTP_DATA_TABLE_XML = FuncSendMail.SerializeListToXml(LS_SenderSmtp)
            My.Settings.SMTP_DATA_TABLE_XML = FuncSendMail.SerializeListToXml(LS_SenderSmtp)
            ' ✅ حفظ محتوى txtLetter
            My.Settings.txtLetter = txtLetter.Text
            ' ✅ حفظ الإعدادات في My.Settings
            My.Settings.Save()
            DevExpress.XtraEditors.XtraMessageBox.Show("Mails, SMTP & Letter content saved Successfully", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Catch ex As Exception
            DevExpress.XtraEditors.XtraMessageBox.Show("Error while saving: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    Private Sub SimpleButton8_Click(sender As Object, e As EventArgs)
        ' فتح نموذج frmLetterinorSP
        Dim frmLetter As New frmLetterinorSP()
        ' إذا كان هناك نص في txtLetter
        If Not String.IsNullOrEmpty(txtLetter.Text) Then
            ' نقل النص من txtLetter إلى txtLetterSP في frmLetterinorSP
            frmLetter.txtLetterSP.Text = txtLetter.Text
        End If
        ' عرض النموذج
        frmLetter.Show()
    End Sub
    Private Sub Switch_LetterConvertorLink_Toggled(sender As Object, e As EventArgs) Handles Switch_LetterConvertorLink.Toggled
        If Switch_LetterConvertorLink.IsOn Then
            txt_LetterConvertorLink.Text = ""
            txt_LetterConvertorLink.ReadOnly = False
            txt_LetterConvertorLink.Properties.NullValuePrompt = "Enter your link..."
            txt_LetterConvertorLink.SelectAll()
            txt_LetterConvertorLink.Focus()
        Else
            txt_LetterConvertorLink.Text = ""
            txt_LetterConvertorLink.ReadOnly = True
        End If
    End Sub
    Private Sub bntResetLetterConvertor_Click_1(sender As Object, e As EventArgs) Handles bntResetLetterConvertor.Click
        'Switch_LetterConvertorLink.IsOn = False
        'Switch_LetterConvertorLink_Toggled(Nothing, Nothing)
    End Sub
    Private Sub ChkLogo_CheckedChanged(sender As Object, e As EventArgs)
        'If ChkLogo.Checked = True Then
        '    bntLetterLogo.Enabled = False
        '    BntResetLetterLogo.Enabled = False
        '    Pic_QRCode.Image = Nothing
        '    lblLogoTitle.Visible = True
        '    bntLetterLogo.Enabled = False
        '    BntResetLetterLogo.Enabled = False
        '    lblLogoTitle.Tag = ""
        '    ChkLogo.ForeColor = Color.FromArgb(34, 203, 121)
        'Else
        '    bntLetterLogo.Enabled = True
        '    BntResetLetterLogo.Enabled = True
        '    lblLogoTitle.Tag = ""
        '    Pic_QRCode.Image = Nothing
        '    lblLogoTitle.Visible = True
        '    bntLetterLogo.Enabled = True
        '    BntResetLetterLogo.Enabled = True
        '    ChkLogo.ForeColor = Color.DarkGray
        'End If
        'If isLogoRemoved > -1 Then
        '    ' إذا تمت إزالة الكلمة "-Logo-" سابقاً، استعادتها
        '    txtLetter.Text = txtLetter.Text.Insert(isLogoRemoved, "[-Logo-]")
        '    isLogoRemoved = -1
        'Else
        '    ' إذا لم يتم إزالة "-Logo-" من قبل، نقوم بحذفها
        '    Dim index As Integer = txtLetter.Text.IndexOf("[-Logo-]")
        '    If index <> -1 Then
        '        txtLetter.Text = txtLetter.Text.Remove(index, "[-Logo-]".Length)
        '        isLogoRemoved = index
        '    End If
        'End If
    End Sub
    Private Sub btnGenerate_Click(sender As Object, e As EventArgs) Handles btnGenerate.Click
        If urlRegex.IsMatch(txtCode.Text) Then
            Try
                Call qrcodeGen()
            Catch ex As Exception
                XtraMessageBox.Show(ex.Message, "Error!", MessageBoxButtons.OK, MessageBoxIcon.Information)
            End Try
        Else
            XtraMessageBox.Show("Please enter a valid link", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        End If
    End Sub
    Private Sub btnExport_Click(sender As Object, e As EventArgs) Handles btnExport.Click
        If Not Pic_QRCode.Image Is Nothing Then
            ' Get the path to the user's desktop
            Dim desktopPath As String = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)
            ' Combine the desktop path with the file name
            Dim fileName As String = Path.Combine(desktopPath, "image.jpg")
            ' Save the image to the desktop
            Try
                Pic_QRCode.Image.Save(fileName, System.Drawing.Imaging.ImageFormat.Jpeg)
                XtraMessageBox.Show("The image has been Successfully saved to your Desktop.", "Save Complete", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Catch ex As Exception
                XtraMessageBox.Show("An Error occurred while saving the image: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        Else
            XtraMessageBox.Show("There is no image to save Please Enter URL!", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End If
    End Sub
    Private Sub SimpleButton3_Click_1(sender As Object, e As EventArgs) Handles SimpleButton3.Click
        txtCode.Text = ""
        Pic_QRCode.Image = Nothing
        'lbl_success_QRcode.Text = ""
        txtHeight.Clear()
        txtWidth.Clear()
    End Sub
    Private Sub txt_limitSend_EditValueChanged(sender As Object, e As EventArgs) Handles txt_limitSend.EditValueChanged
        If chk_limitSend.Checked Then
            lblsuccess_Time.Text = String.Format("Send : {0} Only", Val(txt_limitSend.EditValue))
        End If
    End Sub
    Private Sub chk_limitSend_CheckedChanged(sender As Object, e As EventArgs) Handles chk_limitSend.CheckedChanged
        If chk_limitSend.Checked Then
            txt_limitSend.Enabled = True
            txt_limitSend.Focus()
        Else
            txt_limitSend.EditValue = ""
            lblsuccess_Time.Text = ""
            txt_limitSend.Enabled = False
        End If
    End Sub
    Public Sub Start_Sender()
        Dim isCancel = frmMain.Bnt_Sender_Start.Caption = "Cancel"
        frmMain.Bnt_Sender_Start.ImageOptions.Image = My.Resources.Stop32x32
        If isCancel Then
            cancellationTokenSource.Cancel()
            BackgroundWorker1.CancelAsync()
            Exit Sub
        End If
        If txtLetter.Text.Trim = "" Then
            XtraMessageBox.Show("Please add a letter...!", "Alert", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        ' استبدال الكلمة [-Logo-] بـ <img src="cid:logoImage" />
        'txtLetter.Text = txtLetter.Text.Replace("[-Logo-]", "<img src=""cid:logoImage"" />")
        If GridView1.DataRowCount = 0 Then
            XtraMessageBox.Show("Add atleast one Working Smtp account...!", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        If GridView2.DataRowCount = 0 Then
            XtraMessageBox.Show("Add atleast one Working Email Account...!", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        Dim FailedAccounts As Integer = 0
        Dim uncheckedAccoutns As Integer = 0
        Dim WorkingAccoutns As Integer = 0
        For i = 0 To LS_SenderSmtp.Count - 1
            Select Case LS_SenderSmtp(i).smtpstatus
                Case "Working"
                    WorkingAccoutns = WorkingAccoutns + 1
                Case "Fail"
                    FailedAccounts = FailedAccounts + 1
                Case "Unchecked"
                    uncheckedAccoutns = uncheckedAccoutns + 1
            End Select
        Next
        If FailedAccounts > 0 Then
            XtraMessageBox.Show("You have ( " & FailedAccounts & " ) Fail Smtp Accounts, Please remove the fail accounts and try again...!", "Alert", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        If uncheckedAccoutns > 0 Then
            XtraMessageBox.Show("You have ( " & uncheckedAccoutns & " ) Unchecked Smtp Accounts, please check them and try again...!", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        If WorkingAccoutns = 0 Then
            XtraMessageBox.Show("You have ( 0 ) Working Smtp Accounts. Add aleast one Working account and try again...!", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        Tick_ = 0
        Timer1.Enabled = True
        pnlResult.Visible = True
        pnlWait.Visible = True
        If GridView2.DataRowCount = 0 Then
            XtraMessageBox.Show("Add atleast one email address to start...", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        If GridView1.DataRowCount = 0 Then
            XtraMessageBox.Show("Add atleast one Smtp account to start...", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        frmMain.Bnt_Sender_Start.Caption = "Cancel"
        frmMain.Bnt_Sender_Start.ImageOptions.Image = My.Resources.Stop32x32
        BackgroundWorker1.WorkerSupportsCancellation = True
        BackgroundWorker1.WorkerReportsProgress = True
        '==================================================================================================
        DevExpress.Data.CurrencyDataController.DisableThreadingProblemsDetection = True
        Timer1.Enabled = True
        ProgressBarControl1.Properties.Step = 1
        ProgressBarControl1.Position = 0
        ProgressBarControl1.EditValue = 0
        ProgressBarControl1.Properties.Maximum = MailTable.Rows.Count
        lblSuccess.Text = "Sent : 0"
        lblfailed.Text = "Failed : 0"
        'For i = 0 To LS_SenderMail.Count - 1
        '    LS_SenderMail(i).deliverystatus = "Ready to Receive"
        'Next
        If Not BackgroundWorker1.IsBusy Then
            pnlWait.Visible = True
            isRunning_ = True
            BackgroundWorker1.RunWorkerAsync()
        Else
            isRunning_ = False
            BackgroundWorker1.CancelAsync()
        End If
    End Sub
    Private Sub trcThreads_EditValueChanged(sender As Object, e As EventArgs) Handles trcThreads.EditValueChanged
        lblTrcThreads.Text = trcThreads.Value
    End Sub
    Private Sub bntAddAttachment_Click(sender As Object, e As EventArgs) Handles bntAddAttachment.Click
        If lblattacmentFileCount.Text = "1" Then
            DevExpress.XtraEditors.XtraMessageBox.Show("You already have an attachment file...!", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Exit Sub
        End If
        txtattach.Text = "Attachment"
        Dim dialog As OpenFileDialog = New OpenFileDialog With {
        .Filter = "All files (*.*)|*.*"
    }
        If dialog.ShowDialog() = DialogResult.OK Then
            'txtattach.Text = "Check Payment Attachments[-Name-]"
            txtattachTextGlobal = dialog.FileName
            lblattacmentFileCount.Text = "Attached files : 1"
            lblattacmentFileCount.ForeColor = Color.FromArgb(6, 189, 133)
            txtattach.Text = "Attachment"
            ToolTip2.SetToolTip(lblattacmentFileCount, dialog.FileName)
        End If
    End Sub
    Private Sub BntRemoveAttachment_Click(sender As Object, e As EventArgs) Handles BntRemoveAttachment.Click
        cbTags.SelectedIndex = -1
        ToolTip2.SetToolTip(lblattacmentFileCount, "")
        txtbody.Tag = ""
        txtbody.Text = ""
        txtattach.Text = ""
        txtattach.Clear()
        txtattach.ResetText()
        txtattachTextGlobal = ""
        lblattacmentFileCount.Text = "Attached files : 0"
        lblattacmentFileCount.ForeColor = Color.DarkGray
        If System.IO.Directory.Exists(TempPath) Then
            Try
                For Each deleteFile In Directory.GetFiles(TempPath, "*.*", SearchOption.TopDirectoryOnly)
                    File.Delete(deleteFile)
                Next
            Catch ex As Exception
            End Try
        End If
    End Sub
    Private Sub cbTags_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cbTags.SelectedIndexChanged
        If cbTags.SelectedIndex >= 0 Then
            If cbTags.SelectedItem.ToString() = "Receiver Email" Then
                txtattach.Text = "[-Email-]"
            ElseIf cbTags.SelectedItem.ToString() = "Encoded Email 64" Then
                txtattach.Text = "[-Email64-]"
            ElseIf cbTags.SelectedItem.ToString() = "Start Name With Upper Case" Then
                txtattach.Text = "[-UCase-]"
            ElseIf cbTags.SelectedItem.ToString() = "Add Link" Then
                txtattach.Text = "[-Link-]"
            ElseIf cbTags.SelectedItem.ToString() = "Send With Logo" Then
                txtattach.Text = "[-Logo-]"
            ElseIf cbTags.SelectedItem.ToString() = "Random Character 3" Then
                txtattach.Text = "[-RCh3-]"
            ElseIf cbTags.SelectedItem.ToString() = "Random Character 4" Then
                txtattach.Text = "[-RCh4-]"
            ElseIf cbTags.SelectedItem.ToString() = "Random Character 5" Then
                txtattach.Text = "[-RCh5-]"
            ElseIf cbTags.SelectedItem.ToString() = "Random Character 6" Then
                txtattach.Text = "[-RCh6-]"
            ElseIf cbTags.SelectedItem.ToString() = "Random Character 7" Then
                txtattach.Text = "[-RCh7-]"
            ElseIf cbTags.SelectedItem.ToString() = "Random Number 3" Then
                txtattach.Text = "[-RN3-]"
            ElseIf cbTags.SelectedItem.ToString() = "Random Number 4" Then
                txtattach.Text = "[-RN4-]"
            ElseIf cbTags.SelectedItem.ToString() = "Random Number 5" Then
                txtattach.Text = "[-RN5-]"
            ElseIf cbTags.SelectedItem.ToString() = "Random Number 6" Then
                txtattach.Text = "[-RN6-]"
            ElseIf cbTags.SelectedItem.ToString() = "Random Number 7" Then
                txtattach.Text = "[-RN7-]"
            ElseIf cbTags.SelectedItem.ToString() = "Random IP" Then
                txtattach.Text = "[-IP-]"
            ElseIf cbTags.SelectedItem.ToString() = "Random IP China" Then
                txtattach.Text = "[-IPChina-]"
            ElseIf cbTags.SelectedItem.ToString() = "Show The Domain" Then
                txtattach.Text = "[-Domain-]"
            ElseIf cbTags.SelectedItem.ToString() = "Show Name" Then
                txtattach.Text = "[-Name-]"
            ElseIf cbTags.SelectedItem.ToString() = "Show Company Name" Then
                txtattach.Text = "[-CompanyName-]"
            ElseIf cbTags.SelectedItem.ToString() = "Show Date" Then
                txtattach.Text = "[-Date-]"
            ElseIf cbTags.SelectedItem.ToString() = "Show Time" Then
                txtattach.Text = "[-Time-]"
            ElseIf cbTags.SelectedItem.ToString() = "Show Date Tomorrow" Then
                txtattach.Text = "[-DateTomorrow-]"
            ElseIf cbTags.SelectedItem.ToString() = "Random Country" Then
                txtattach.Text = "[-RCountry-]"
            ElseIf cbTags.SelectedItem.ToString() = "Random Browser" Then
                txtattach.Text = "[-RandomBrowser-]"
            ElseIf cbTags.SelectedItem.ToString() = "Show Random Fake phone Numbers" Then
                txtattach.Text = "[-FakePhone-]"
            ElseIf cbTags.SelectedItem.ToString() = "Show Random Fake Email Address Office" Then
                txtattach.Text = "[-FakeEmail-]"
            ElseIf cbTags.SelectedItem.ToString() = "Show Random Street New York" Then
                txtattach.Text = "[-NewYork-]"
            End If
        End If
    End Sub
    Private Sub bntLetterLogo_Click(sender As Object, e As EventArgs) Handles bntLetterLogo.Click
        Dim ofd As New OpenFileDialog
        With ofd
            .InitialDirectory = "C:\"
            .Filter = "PNG|*.png|JPEGs|*.jpg|GIFs|*.gif|Bitmaps|*.bmp|All Files|*.*"
            .FilterIndex = 1
        End With
        If ofd.ShowDialog() = Windows.Forms.DialogResult.OK Then
            lblLogoTitle.Visible = False
            With piclogo
                .Image = System.Drawing.Image.FromFile(ofd.FileName)
                .SizeMode = PictureBoxSizeMode.Zoom
                .BorderStyle = BorderStyle.FixedSingle
            End With
            ToolTip3.SetToolTip(piclogo, ofd.FileName)
            lblLogoTitle.Tag = ofd.FileName
        End If
    End Sub
    Private Sub BntResetLetterLogo_Click(sender As Object, e As EventArgs) Handles BntResetLetterLogo.Click
        piclogo.Image = Nothing
        ToolTip3.SetToolTip(piclogo, "")
        lblLogoTitle.Visible = True
        lblLogoTitle.Tag = ""
    End Sub
    Private Sub GridControl2_MouseDown(sender As Object, e As MouseEventArgs) Handles GridControl2.MouseDown
        ' التحقق من أن النقر بالزر الأيمن
        If e.Button = MouseButtons.Right Then
            If SmptTestWait.Visible = True Then Exit Sub
            Dim view As Views.Grid.GridView = TryCast(GridControl2.GetViewAt(e.Location), Views.Grid.GridView)
            If view IsNot Nothing Then
                PopupMenu2.ShowPopup(GridControl2.PointToScreen(e.Location))
            End If
        End If
    End Sub
    Private Sub BntAddMailList_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BntAddMailList.ItemClick
        Add_Mail_List()
    End Sub
    Private Sub BntClearEList_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BntClearEList.ItemClick
        Clear_Mail_List()
    End Sub
    Private Sub txtLetter_MouseDown(sender As Object, e As MouseEventArgs) Handles txtLetter.MouseDown
        If e.Button = MouseButtons.Right Then
            ' عرض القائمة المنبثقة عند موقع النقر
            PopupMenu3.ShowPopup(txtLetter.PointToScreen(e.Location))
        End If
    End Sub
    Private Sub BarButtonItem13_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem13.ItemClick
        Add_Letter()
    End Sub
    Private Sub BarButtonItem14_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem14.ItemClick
        Clear_Letter()
        txtNewLink.Clear()
    End Sub
    Private Sub BarButtonItem16_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem16.ItemClick
        If Clipboard.ContainsText() Then
            txtLetter.Text = Clipboard.GetText()
        Else
            XtraMessageBox.Show("Clipboard does not contain any text.", "Notice", MessageBoxButtons.OK, MessageBoxIcon.Information)
        End If
    End Sub
    Private Sub frmEmailSender_FormClosed(sender As Object, e As FormClosedEventArgs) Handles MyBase.FormClosed
        Dim mainForm As Form = System.Windows.Forms.Application.OpenForms("frmMain")
        If mainForm IsNot Nothing Then
            With CType(mainForm, frmMain)
                .BarSubItem4.Enabled = False
                .BarSubItem3.Enabled = False
                .BarSubItem5.Enabled = False

                .Bnt_Sender_Start.Enabled = False
                '.Bnt_Sender_Stop.Enabled = False
                .Bnt_Tag.Enabled = False
                .Bnt_Save_AllSettings.Enabled = False
                .Bnt_Reset.Enabled = False
                .BntSubject.Enabled = False

                .RibbonPageGroup16.Visible = False
                .RibbonPageGroup13.Visible = False
                .RibbonPageGroup15.Visible = False
                .RibbonPageGroup24.Visible = False
                .RibbonPageGroup14.Visible = False
                .RibbonPageGroup4.Visible = False
                .RibbonPageGroup12.Visible = False
                .RibbonPageGroup66.Visible = False
            End With
        End If


    End Sub
    Private Sub txtLetter_MouseEnter(sender As Object, e As EventArgs) Handles txtLetter.MouseEnter
        txtLetter.ForeColor = Color.FromArgb(254, 219, 65)
        txtLetter.BackColor = Color.FromArgb(26, 26, 29)
    End Sub
    Private Sub txtLetter_MouseLeave(sender As Object, e As EventArgs) Handles txtLetter.MouseLeave
        txtLetter.BackColor = Color.FromArgb(26, 26, 29)
        txtLetter.ForeColor = Color.White
    End Sub
    Private Sub GridView1_CustomDrawCell(sender As Object, e As RowCellCustomDrawEventArgs) Handles GridView1.CustomDrawCell
        ' Handle specific columns
        If e.Column.FieldName = "id" Then
            ' Draw the ID number only (no icons)
            Dim idValue As String = ""
            If e.RowHandle >= 0 AndAlso GridView1.GetRowCellValue(e.RowHandle, "id") IsNot Nothing Then
                idValue = GridView1.GetRowCellValue(e.RowHandle, "id").ToString()
            End If
            ' Set background color based on even/odd row (Binance-inspired)
            Dim backgroundColor As Color
            If e.RowHandle Mod 2 = 0 Then
                backgroundColor = Color.FromArgb(18, 22, 28) ' Even rows
            Else
                backgroundColor = Color.FromArgb(30, 35, 41) ' Odd rows
            End If
            ' If row is selected, override the background color
            If GridView1.IsRowSelected(e.RowHandle) Then
                backgroundColor = Color.FromArgb(47, 52, 59)
            End If
            ' Apply the background color
            Using brush As New SolidBrush(backgroundColor)
                e.Graphics.FillRectangle(brush, e.Bounds)
            End Using
            ' Set text color to white
            e.Appearance.ForeColor = Color.FromArgb(255, 255, 255)
            e.Appearance.Options.UseForeColor = True
            ' Set font to Segoe UI with 10pt size
            Try
                e.Appearance.Font = New Font("Segoe UI", 10)
                e.Appearance.Options.UseFont = True
            Catch ex As Exception
                ' If there's an error with the font, just use the default
            End Try
            ' Center the ID text
            e.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
            ' Just draw the ID text without any icons
            e.Appearance.DrawString(e.Cache, idValue, e.Bounds, e.Appearance.GetStringFormat())
            ' Make sure the event is handled
            e.Handled = True
        ElseIf e.Column.FieldName = "smtpstatus" Then
            ' Get the status value
            Dim statusValue As String = ""
            If e.CellValue IsNot Nothing Then
                statusValue = e.CellValue.ToString()
            End If
            ' Set background color based on even/odd row (Binance-inspired)
            Dim backgroundColor As Color
            If e.RowHandle Mod 2 = 0 Then
                backgroundColor = Color.FromArgb(18, 22, 28) ' Even rows
            Else
                backgroundColor = Color.FromArgb(30, 35, 41) ' Odd rows
            End If
            ' If row is selected, override the background color
            If GridView1.IsRowSelected(e.RowHandle) Then
                backgroundColor = Color.FromArgb(47, 52, 59)
            End If
            ' Apply the background color
            Using brush As New SolidBrush(backgroundColor)
                e.Graphics.FillRectangle(brush, e.Bounds)
            End Using
            ' Determine which icon to use and set Binance-inspired colors
            Dim icon As System.Drawing.Bitmap = Nothing
            Select Case statusValue
                Case "Working"
                    icon = DirectCast(My.Resources.Valid16x16, System.Drawing.Bitmap)
                    ' Positive values (profits) - Binance green
                    e.Appearance.ForeColor = Color.FromArgb(0, 194, 146)
                    e.Appearance.Font = New Font("Segoe UI", 10, System.Drawing.FontStyle.Bold)
                Case "Unchecked"
                    icon = DirectCast(My.Resources.pending16x16, System.Drawing.Bitmap)
                    ' Neutral values - Soft yellow/orange
                    e.Appearance.ForeColor = Color.FromArgb(254, 153, 35)
                    e.Appearance.Font = New Font("Segoe UI", 10, System.Drawing.FontStyle.Bold)
                Case "Fail"
                    icon = DirectCast(My.Resources.Notworking16x16, System.Drawing.Bitmap)
                    ' Negative values (losses) - Binance red
                    e.Appearance.ForeColor = Color.FromArgb(255, 76, 76)
                    e.Appearance.Font = New Font("Segoe UI", 10, System.Drawing.FontStyle.Bold)
                Case "Verifing"
                    icon = DirectCast(My.Resources.pending16x16, System.Drawing.Bitmap)
                    ' Processing status - Yellow
                    e.Appearance.ForeColor = Color.FromArgb(254, 219, 65)
                    e.Appearance.Font = New Font("Segoe UI", 10, System.Drawing.FontStyle.Bold)
            End Select
            e.Appearance.Options.UseFont = True
            e.Appearance.Options.UseForeColor = True
            ' If we have an icon, draw it
            If icon IsNot Nothing Then
                ' Calculate positions
                Dim iconRect As Rectangle = New Rectangle(e.Bounds.X + 5, e.Bounds.Y + (e.Bounds.Height - 16) \ 2, 16, 16)
                Dim textRect As Rectangle = New Rectangle(iconRect.Right + 5, e.Bounds.Y, e.Bounds.Width - iconRect.Width - 10, e.Bounds.Height)
                ' Draw the icon - use the correct overload
                e.Graphics.DrawImage(icon, iconRect.X, iconRect.Y, iconRect.Width, iconRect.Height)
                ' Draw the text
                e.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
                e.Appearance.DrawString(e.Cache, statusValue, textRect)
                ' Indicate that we've handled the drawing
                e.Handled = True
            End If
        End If
    End Sub
    ' دالة لتهيئة تمركز العناصر
    Private Sub InitializeCenteringControls()
        Try
            ' تعيين الحجم الثابت لعناصر GroupControl
            SetGroupControlsFixedSize()
            ' تمركز عناصر GroupControl داخل XtraTabControl2
            CenterGroupControlsInTabControl()
            ' إضافة معالج حدث تغيير التبويب المحدد
            AddHandler XtraTabControl1.SelectedPageChanged, AddressOf XtraTabControl1_SelectedPageChanged
        Catch ex As Exception
            ' معالجة الأخطاء
        End Try
    End Sub
    ' دالة لتمركز XtraTabControl2 داخل XtraTabControl1
    Private Sub CenterXtraTabControl2()
        Try
            ' تمركز XtraTabControl2 في منتصف XtraTabControl1
            If XtraTabControl1.SelectedTabPage IsNot Nothing AndAlso XtraTabControl2 IsNot Nothing Then
                ' حساب موقع XtraTabControl2 لتمركزه أفقيًا وعموديًا
                Dim x As Integer = (XtraTabControl1.SelectedTabPage.Width - XtraTabControl2.Width) \ 2
                Dim y As Integer = (XtraTabControl1.SelectedTabPage.Height - XtraTabControl2.Height) \ 2
                ' تعيين موقع XtraTabControl2
                XtraTabControl2.Location = New System.Drawing.Point(Math.Max(0, x), Math.Max(0, y))
                ' تمركز عناصر GroupControl داخل XtraTabControl2
                CenterGroupControlsInTabControl()
            End If
        Catch ex As Exception
            ' معالجة الأخطاء
        End Try
    End Sub
    ' دالة لتعيين الحجم الثابت لعناصر GroupControl
    Private Sub SetGroupControlsFixedSize()
        Try
            ' تعيين الحجم الثابت لجميع عناصر GroupControl المطلوبة
            If GroupControl4 IsNot Nothing Then GroupControl4.Size = New System.Drawing.Size(416, 327)
            If GroupControl5 IsNot Nothing Then GroupControl5.Size = New System.Drawing.Size(416, 327)
            If GroupControl6 IsNot Nothing Then GroupControl6.Size = New System.Drawing.Size(416, 327)
            If GroupControl7 IsNot Nothing Then GroupControl7.Size = New System.Drawing.Size(416, 327)
            If GroupControl8 IsNot Nothing Then GroupControl8.Size = New System.Drawing.Size(416, 327)
            If GroupControl9 IsNot Nothing Then GroupControl9.Size = New System.Drawing.Size(416, 327)
        Catch ex As Exception
            ' معالجة الأخطاء
        End Try
    End Sub
    ' دالة لتمركز عناصر GroupControl داخل XtraTabControl2
    Private Sub CenterGroupControlsInTabControl()
        Try
            ' تمركز كل GroupControl في صفحة التبويب الخاصة به
            For Each tabPage As DevExpress.XtraTab.XtraTabPage In XtraTabControl2.TabPages
                For Each ctrl As System.Windows.Forms.Control In tabPage.Controls
                    If TypeOf ctrl Is DevExpress.XtraEditors.GroupControl Then
                        Dim groupCtrl As DevExpress.XtraEditors.GroupControl = TryCast(ctrl, DevExpress.XtraEditors.GroupControl)
                        If groupCtrl IsNot Nothing Then
                            ' حساب موقع GroupControl لتمركزه أفقيًا وعموديًا
                            Dim x As Integer = (tabPage.Width - groupCtrl.Width) \ 2
                            Dim y As Integer = (tabPage.Height - groupCtrl.Height) \ 2
                            ' تعيين موقع GroupControl
                            groupCtrl.Location = New System.Drawing.Point(Math.Max(0, x), Math.Max(0, y))
                        End If
                    End If
                Next
            Next
        Catch ex As Exception
            ' معالجة الأخطاء
        End Try
    End Sub
    ' معالج حدث تغيير التبويب المحدد في XtraTabControl1
    Private Sub XtraTabControl1_SelectedPageChanged(sender As Object, e As DevExpress.XtraTab.TabPageChangedEventArgs)
        Try
            ' إعادة تمركز XtraTabControl2 عند تغيير التبويب المحدد
            CenterXtraTabControl2()
        Catch ex As Exception
            ' معالجة الأخطاء
        End Try
    End Sub
    ' معالج حدث تغيير حجم النموذج
    Private Sub frmEmailSender_Resize(sender As Object, e As EventArgs) Handles Me.Resize
        Try
            ' إعادة تمركز XtraTabControl2 عند تغيير حجم النموذج
            CenterXtraTabControl2()
            ' إعادة تمركز SmptTestWait عند تغيير حجم النموذج
            CenterSmptTestWait()
        Catch ex As Exception
            ' معالجة الأخطاء
        End Try
    End Sub
    ' دالة لتمركز SmptTestWait في وسط Panel2
    Private Sub CenterSmptTestWait()
        If SmptTestWait IsNot Nothing AndAlso Panel2 IsNot Nothing Then
            ' حساب الموضع المركزي
            Dim x As Integer = (Panel2.Width - SmptTestWait.Width) \ 2
            Dim y As Integer = (Panel2.Height - SmptTestWait.Height) \ 2
            ' تعيين موضع SmptTestWait باستخدام System.Drawing.Point
            SmptTestWait.Location = New System.Drawing.Point(x, y)
        End If
    End Sub


    Private Sub BarButtonItem17_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem17.ItemClick
        'OpenForm(Child_frmEditLetter, Me)
    End Sub

    Private Sub BntAddFromName_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BntAddFromName.ItemClick
        LoadTextFileToList(FieldType.FromName)
    End Sub

    Private Sub BntAddSubject_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BntAddSubject.ItemClick
        LoadTextFileToList(FieldType.Subject)
    End Sub

    Private Sub BntFromMail_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BntFromMail.ItemClick
        LoadTextFileToList(FieldType.FromMail)
    End Sub

    Private Enum FieldType
        FromName
        Subject
        FromMail
    End Enum

    ' ✅ الدالة الجديدة لتحميل الملفات النصية إلى القوائم المنفصلة
    Private Sub LoadTextFileToList(fieldType As FieldType)
        Using openFileDialog As New OpenFileDialog()
            openFileDialog.Filter = "Text Files (*.txt)|*.txt|All Files (*.*)|*.*"
            openFileDialog.InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)

            If openFileDialog.ShowDialog() = DialogResult.OK Then
                Try
                    Dim lines() As String = File.ReadAllLines(openFileDialog.FileName)
                    If lines.Length = 0 Then
                        XtraMessageBox.Show("The selected file is empty.", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                        Return
                    End If

                    ' تنظيف القائمة المناسبة وإضافة البيانات الجديدة
                    Select Case fieldType
                        Case FieldType.FromName
                            FromNamesList.Clear()
                            FromNamesList.AddRange(lines.Where(Function(line) Not String.IsNullOrWhiteSpace(line)))

                        Case FieldType.Subject
                            SubjectsList.Clear()
                            SubjectsList.AddRange(lines.Where(Function(line) Not String.IsNullOrWhiteSpace(line)))

                        Case FieldType.FromMail
                            FromEmailsList.Clear()
                            FromEmailsList.AddRange(lines.Where(Function(line) Not String.IsNullOrWhiteSpace(line)))
                    End Select

                    ' إضافة أيقونة Change16x16 في العمود المناسب لكل عنصر في GridControl2
                    AddIconsToGridControl2(fieldType)

                    ' حفظ القوائم في الإعدادات
                    SaveRotationListsToSettings()

                    ' عرض رسالة نجاح مع عدد العناصر
                    Dim fieldName As String = ""
                    Dim count As Integer = 0
                    Select Case fieldType
                        Case FieldType.FromName
                            fieldName = "From Name"
                            count = FromNamesList.Count
                        Case FieldType.Subject
                            fieldName = "Subjects"
                            count = SubjectsList.Count
                        Case FieldType.FromMail
                            fieldName = "From Email"
                            count = FromEmailsList.Count
                    End Select

                    XtraMessageBox.Show($"{fieldName} list has been successfully loaded with {count} items.", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)

                    ' عرض إحصائيات القوائم
                    UpdateRotationListsStatus()

                    ' عرض معلومات إضافية عن القوائم المحملة
                    ShowRotationListsInfo()

                Catch ex As Exception
                    XtraMessageBox.Show("An error occurred while reading the file: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                End Try
            End If
        End Using
    End Sub

    ' ✅ الدالة الأصلية للتوافق مع الكود الموجود
    Private Sub LoadTextFileAndProcess(fieldType As FieldType)
        Using openFileDialog As New OpenFileDialog()
            openFileDialog.Filter = "Text Files (*.txt)|*.txt|All Files (*.*)|*.*"
            If openFileDialog.ShowDialog() = DialogResult.OK Then
                Try
                    Dim lines() As String = File.ReadAllLines(openFileDialog.FileName)
                    If lines.Length = 0 Then
                        XtraMessageBox.Show("The selected file is empty.", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                        Return
                    End If

                    Dim currentIndex As Integer = 0

                    For Each smtpSetting As SenderSmtpSettings In LS_SenderSmtp
                        If currentIndex >= lines.Length Then
                            currentIndex = 0 ' إعادة التدوير إلى بداية الملف
                        End If

                        Select Case fieldType
                            Case FieldType.FromName
                                smtpSetting.smtpfromname = lines(currentIndex)
                            Case FieldType.Subject
                                smtpSetting.smtpsubject = lines(currentIndex)
                            Case FieldType.FromMail
                                smtpSetting.smtpfrommail = lines(currentIndex)
                        End Select
                        currentIndex += 1
                    Next

                    ' تحديث GridControl1 لعرض البيانات الجديدة
                    GridControl1.RefreshDataSource()
                    GridView1.RefreshData()

                    ' حفظ الإعدادات
                    My.Settings.SMTP_DATA_TABLE_XML = FuncSendMail.SerializeListToXml(LS_SenderSmtp)
                    My.Settings.Save()

                    ' عرض رسالة نجاح
                    Dim fieldName As String = ""
                    Select Case fieldType
                        Case FieldType.FromName
                            fieldName = "From Name"
                        Case FieldType.Subject
                            fieldName = "Subjects"
                        Case FieldType.FromMail
                            fieldName = "Email From"
                    End Select

                    XtraMessageBox.Show($"{fieldName} has been successfully updated.", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
                Catch ex As Exception
                    XtraMessageBox.Show("An error occurred while reading the file or updating the data: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                End Try

            End If
        End Using
    End Sub

    ' ✅ دالة إضافة الأيقونات في GridControl1 (نفس مستوى SMTP)
    Private Sub AddIconsToGridControl2(fieldType As FieldType)
        Try
            ' التأكد من وجود بيانات في GridControl1 (SMTP)
            If GridView1.DataRowCount = 0 Then Return

            ' تحديد العمود المناسب حسب نوع البيانات في GridControl1
            Dim targetColumn As DevExpress.XtraGrid.Columns.GridColumn = Nothing
            Select Case fieldType
                Case FieldType.Subject
                    targetColumn = GridColumn4  ' عمود Subject في GridControl1
                Case FieldType.FromName
                    targetColumn = GridColumn11 ' عمود From Name في GridControl1
                Case FieldType.FromMail
                    targetColumn = GridColumn7  ' عمود From Email في GridControl1
            End Select

            If targetColumn IsNot Nothing Then
                ' إضافة أيقونة Valid16x16 لكل صف في العمود المحدد
                For i As Integer = 0 To GridView1.DataRowCount - 1
                    Try
                        ' إضافة أيقونة Valid16x16.png في الخلية
                        ' يمكن استخدام RepositoryItemImageEdit أو تعديل خصائص العمود

                        ' طريقة 1: تعديل قيمة الخلية لتتضمن رمز الأيقونة
                        Dim cellValue As Object = GridView1.GetRowCellValue(i, targetColumn)
                        Dim currentValue As String = If(cellValue IsNot Nothing, cellValue.ToString(), "")
                        If Not String.IsNullOrEmpty(currentValue) AndAlso Not currentValue.Contains("✓") Then
                            GridView1.SetRowCellValue(i, targetColumn, "✓ " & currentValue)
                        End If

                    Catch cellEx As Exception
                        ' تجاهل أخطاء الخلايا الفردية
                    End Try
                Next

                ' طريقة 2: تعديل خصائص العمود لإظهار أيقونة
                Try
                    ' محاولة الحصول على أيقونة Valid16x16 من الموارد
                    Dim validIcon As System.Drawing.Image = Nothing
                    Try
                        ' محاولة الحصول على الأيقونة من الموارد
                        Dim resourceManager As System.Resources.ResourceManager = My.Resources.ResourceManager
                        Dim iconObject As Object = resourceManager.GetObject("Valid16x16")
                        If iconObject IsNot Nothing AndAlso TypeOf iconObject Is System.Drawing.Bitmap Then
                            validIcon = DirectCast(iconObject, System.Drawing.Image)
                        End If
                    Catch resEx As Exception
                        ' إذا لم تكن الأيقونة متوفرة في الموارد، استخدم رمز نصي
                        validIcon = Nothing
                    End Try

                    ' إضافة أيقونة في رأس العمود للإشارة إلى وجود قائمة محملة
                    Select Case fieldType
                        Case FieldType.Subject
                            If SubjectsList.Count > 0 Then
                                targetColumn.Caption = "✓ Subject (" & SubjectsList.Count.ToString() & ")"
                                ' إضافة الأيقونة إذا كانت متوفرة
                                If validIcon IsNot Nothing Then
                                    ' يمكن إضافة الأيقونة هنا إذا كان DevExpress يدعم ذلك
                                End If
                            End If
                        Case FieldType.FromName
                            If FromNamesList.Count > 0 Then
                                targetColumn.Caption = "✓ From Name (" & FromNamesList.Count.ToString() & ")"
                                If validIcon IsNot Nothing Then
                                    ' يمكن إضافة الأيقونة هنا إذا كان DevExpress يدعم ذلك
                                End If
                            End If
                        Case FieldType.FromMail
                            If FromEmailsList.Count > 0 Then
                                targetColumn.Caption = "✓ From Mail (" & FromEmailsList.Count.ToString() & ")"
                                If validIcon IsNot Nothing Then
                                    ' يمكن إضافة الأيقونة هنا إذا كان DevExpress يدعم ذلك
                                End If
                            End If
                    End Select
                Catch headerEx As Exception
                    ' تجاهل أخطاء تعديل الرأس
                End Try
            End If

            ' تحديث العرض في GridControl1 (SMTP)
            GridView1.RefreshData()
            GridControl1.RefreshDataSource()

        Catch ex As Exception
            ' تجاهل الأخطاء في إضافة الأيقونات
        End Try
    End Sub

    ' ✅ دالة للحصول على عنصر عشوائي من القائمة
    Private Function GetRandomFromList(list As List(Of String)) As String
        If list Is Nothing OrElse list.Count = 0 Then
            Return String.Empty
        End If
        Return list(Random.Next(0, list.Count))
    End Function

    ' ✅ دالة للحصول على البيانات العشوائية للإرسال
    Private Function GetRandomSendingData() As (Subject As String, FromName As String, FromEmail As String)
        Return (
            Subject:=GetRandomFromList(SubjectsList),
            FromName:=GetRandomFromList(FromNamesList),
            FromEmail:=GetRandomFromList(FromEmailsList)
        )
    End Function

    ' ✅ حفظ القوائم في الإعدادات
    Private Sub SaveRotationListsToSettings()
        Try
            ' استخدام طريقة بديلة للحفظ إذا لم تكن الإعدادات متوفرة
            Try
                My.Settings.Item("SUBJECTS_LIST") = String.Join("|", SubjectsList)
                My.Settings.Item("FROM_NAMES_LIST") = String.Join("|", FromNamesList)
                My.Settings.Item("FROM_EMAILS_LIST") = String.Join("|", FromEmailsList)
                My.Settings.Save()
            Catch settingsEx As Exception
                ' استخدام ملف نصي كبديل
                SaveRotationListsToFile()
            End Try
        Catch ex As Exception
            ' تجاهل أخطاء الحفظ
        End Try
    End Sub

    ' ✅ استرجاع القوائم من الإعدادات
    Private Sub LoadRotationListsFromSettings()
        Try
            Try
                Dim subjectsList_Setting = My.Settings.Item("SUBJECTS_LIST")
                If subjectsList_Setting IsNot Nothing AndAlso Not String.IsNullOrEmpty(subjectsList_Setting.ToString()) Then
                    SubjectsList.Clear()
                    SubjectsList.AddRange(subjectsList_Setting.ToString().Split("|"c).Where(Function(s) Not String.IsNullOrWhiteSpace(s)))
                End If

                Dim fromNamesList_Setting = My.Settings.Item("FROM_NAMES_LIST")
                If fromNamesList_Setting IsNot Nothing AndAlso Not String.IsNullOrEmpty(fromNamesList_Setting.ToString()) Then
                    FromNamesList.Clear()
                    FromNamesList.AddRange(fromNamesList_Setting.ToString().Split("|"c).Where(Function(s) Not String.IsNullOrWhiteSpace(s)))
                End If

                Dim fromEmailsList_Setting = My.Settings.Item("FROM_EMAILS_LIST")
                If fromEmailsList_Setting IsNot Nothing AndAlso Not String.IsNullOrEmpty(fromEmailsList_Setting.ToString()) Then
                    FromEmailsList.Clear()
                    FromEmailsList.AddRange(fromEmailsList_Setting.ToString().Split("|"c).Where(Function(s) Not String.IsNullOrWhiteSpace(s)))
                End If
            Catch settingsEx As Exception
                ' استخدام ملف نصي كبديل
                LoadRotationListsFromFile()
            End Try
        Catch ex As Exception
            ' تجاهل أخطاء الاسترجاع
        End Try
    End Sub

    ' ✅ حفظ القوائم في ملف نصي كبديل
    Private Sub SaveRotationListsToFile()
        Try
            Dim settingsPath As String = Path.Combine(System.AppDomain.CurrentDomain.BaseDirectory, "RotationLists.txt")
            Dim lines As New List(Of String)
            lines.Add($"SUBJECTS_LIST={String.Join("|", SubjectsList)}")
            lines.Add($"FROM_NAMES_LIST={String.Join("|", FromNamesList)}")
            lines.Add($"FROM_EMAILS_LIST={String.Join("|", FromEmailsList)}")
            File.WriteAllLines(settingsPath, lines)
        Catch ex As Exception
            ' تجاهل أخطاء الحفظ
        End Try
    End Sub

    ' ✅ استرجاع القوائم من ملف نصي كبديل
    Private Sub LoadRotationListsFromFile()
        Try
            Dim settingsPath As String = Path.Combine(System.AppDomain.CurrentDomain.BaseDirectory, "RotationLists.txt")
            If File.Exists(settingsPath) Then
                Dim lines() As String = File.ReadAllLines(settingsPath)
                For Each line As String In lines
                    If line.StartsWith("SUBJECTS_LIST=") Then
                        Dim value As String = line.Substring("SUBJECTS_LIST=".Length)
                        If Not String.IsNullOrEmpty(value) Then
                            SubjectsList.Clear()
                            SubjectsList.AddRange(value.Split("|"c).Where(Function(s) Not String.IsNullOrWhiteSpace(s)))
                        End If
                    ElseIf line.StartsWith("FROM_NAMES_LIST=") Then
                        Dim value As String = line.Substring("FROM_NAMES_LIST=".Length)
                        If Not String.IsNullOrEmpty(value) Then
                            FromNamesList.Clear()
                            FromNamesList.AddRange(value.Split("|"c).Where(Function(s) Not String.IsNullOrWhiteSpace(s)))
                        End If
                    ElseIf line.StartsWith("FROM_EMAILS_LIST=") Then
                        Dim value As String = line.Substring("FROM_EMAILS_LIST=".Length)
                        If Not String.IsNullOrEmpty(value) Then
                            FromEmailsList.Clear()
                            FromEmailsList.AddRange(value.Split("|"c).Where(Function(s) Not String.IsNullOrWhiteSpace(s)))
                        End If
                    End If
                Next
            End If
        Catch ex As Exception
            ' تجاهل أخطاء الاسترجاع
        End Try
    End Sub

    ' ✅ تحديث حالة القوائم وعرض الإحصائيات
    Private Sub UpdateRotationListsStatus()
        Try
            ' يمكن إضافة كود هنا لتحديث واجهة المستخدم بعدد العناصر في كل قائمة
            ' مثال: تحديث تسميات أو أشرطة الحالة

            ' عرض رسالة في وحدة التحكم أو سجل للمطور
            System.Diagnostics.Debug.WriteLine($"Rotation Lists Status:")
            System.Diagnostics.Debug.WriteLine($"- Subjects: {SubjectsList.Count} items")
            System.Diagnostics.Debug.WriteLine($"- From Names: {FromNamesList.Count} items")
            System.Diagnostics.Debug.WriteLine($"- From Emails: {FromEmailsList.Count} items")

        Catch ex As Exception
            ' تجاهل الأخطاء
        End Try
    End Sub

    ' ✅ عرض معلومات القوائم المحملة
    Private Sub ShowRotationListsInfo()
        Try
            Dim info As New StringBuilder()
            info.AppendLine("📊 Rotation Lists Status:")
            info.AppendLine($"• Subjects: {SubjectsList.Count} items")
            info.AppendLine($"• From Names: {FromNamesList.Count} items")
            info.AppendLine($"• From Emails: {FromEmailsList.Count} items")

            If SubjectsList.Count > 0 OrElse FromNamesList.Count > 0 OrElse FromEmailsList.Count > 0 Then
                info.AppendLine()
                info.AppendLine("✅ Random rotation is now active for sending emails!")
            Else
                info.AppendLine()
                info.AppendLine("ℹ️ No rotation lists loaded. Using default SMTP settings.")
            End If

            ' يمكن عرض هذه المعلومات في وحدة التحكم أو في رسالة منبثقة
            System.Diagnostics.Debug.WriteLine(info.ToString())

        Catch ex As Exception
            ' تجاهل الأخطاء
        End Try
    End Sub

    Private Sub BntClearFromName_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BntClearFromName.ItemClick
        ' مسح قائمة أسماء المرسل
        Dim result As DialogResult = DevExpress.XtraEditors.XtraMessageBox.Show("Do you want to clear the From Name rotation list?", "Confirmation", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2)
        If result = DialogResult.Yes Then
            FromNamesList.Clear()
            SaveRotationListsToSettings()
            ' إعادة تعيين عنوان العمود
            ResetColumnCaption(FieldType.FromName)
            DevExpress.XtraEditors.XtraMessageBox.Show("From Name rotation list has been cleared.", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
        End If
    End Sub

    Private Sub BntClearAllSubject_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BntClearAllSubject.ItemClick
        ' مسح قائمة المواضيع
        Dim result As DialogResult = DevExpress.XtraEditors.XtraMessageBox.Show("Do you want to clear the Subjects rotation list?", "Confirmation", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2)
        If result = DialogResult.Yes Then
            SubjectsList.Clear()
            SaveRotationListsToSettings()
            ' إعادة تعيين عنوان العمود
            ResetColumnCaption(FieldType.Subject)
            DevExpress.XtraEditors.XtraMessageBox.Show("Subjects rotation list has been cleared.", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
        End If
    End Sub

    Private Sub BntClearAllFromEmail_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BntClearAllFromEmail.ItemClick
        ' مسح قائمة بريد المرسل
        Dim result As DialogResult = DevExpress.XtraEditors.XtraMessageBox.Show("Do you want to clear the From Email rotation list?", "Confirmation", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2)
        If result = DialogResult.Yes Then
            FromEmailsList.Clear()
            SaveRotationListsToSettings()
            ' إعادة تعيين عنوان العمود
            ResetColumnCaption(FieldType.FromMail)
            DevExpress.XtraEditors.XtraMessageBox.Show("From Email rotation list has been cleared.", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
        End If
    End Sub

    ' ✅ إعادة تعيين عنوان العمود إلى الحالة الأصلية
    Private Sub ResetColumnCaption(fieldType As FieldType)
        Try
            Select Case fieldType
                Case FieldType.Subject
                    If GridColumn4 IsNot Nothing Then
                        GridColumn4.Caption = "Subject"
                    End If
                Case FieldType.FromName
                    If GridColumn11 IsNot Nothing Then
                        GridColumn11.Caption = "From Name"
                    End If
                Case FieldType.FromMail
                    If GridColumn7 IsNot Nothing Then
                        GridColumn7.Caption = "From Mail"
                    End If
            End Select

            ' تحديث العرض في GridControl1 (SMTP)
            GridView1.RefreshData()
            GridControl1.RefreshDataSource()

        Catch ex As Exception
            ' تجاهل الأخطاء
        End Try
    End Sub
    Public Sub ClearFromName()
        'clear All From Name
        If GridView1.DataRowCount = 0 Then
            DevExpress.XtraEditors.XtraMessageBox.Show("The list is empty...", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If

        Dim result As DialogResult = DevExpress.XtraEditors.XtraMessageBox.Show("You Like Delete All From Name", "Confirmation", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2)
        If result = DialogResult.No Then Exit Sub

        For i = 0 To GridView1.DataRowCount - 1
            GridView1.SetRowCellValue(i, "smtpfromname", "")
        Next
        GridView1.RefreshData()
    End Sub

    Public Sub ClearSubject()
        'clear All subject
        If GridView1.DataRowCount = 0 Then
            DevExpress.XtraEditors.XtraMessageBox.Show("The list is empty...", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If

        Dim result As DialogResult = DevExpress.XtraEditors.XtraMessageBox.Show("You Like Delete All Subject", "Confirmation", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2)
        If result = DialogResult.No Then Exit Sub

        For i = 0 To GridView1.DataRowCount - 1
            GridView1.SetRowCellValue(i, "smtpsubject", "")
        Next
        GridView1.RefreshData()
    End Sub

    Public Sub ClearFromMail()
        'clear All From Email
        If GridView1.DataRowCount = 0 Then
            DevExpress.XtraEditors.XtraMessageBox.Show("The list is empty...", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If

        Dim result As DialogResult = DevExpress.XtraEditors.XtraMessageBox.Show("You Like Delete All Email From", "Confirmation", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2)
        If result = DialogResult.No Then Exit Sub

        For i = 0 To GridView1.DataRowCount - 1
            GridView1.SetRowCellValue(i, "smtpfrommail", "")
        Next
        GridView1.RefreshData()
    End Sub
    Private Sub ColorPickEdit1BK_EditValueChanged(sender As Object, e As EventArgs) Handles ColorPickEdit1BK.EditValueChanged
        useCustomColors = True
        qrcodeGen()
    End Sub

    Private Sub ColorPickEdit1qrcode_EditValueChanged(sender As Object, e As EventArgs) Handles ColorPickEdit1qrcode.EditValueChanged
        useCustomColors = True
        qrcodeGen()
    End Sub

    Private Sub txtCode_TextChanged(sender As Object, e As EventArgs) Handles txtCode.TextChanged
        qrcodeGen()
    End Sub

    Private Sub BntClearAllSMTPRemove_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BntClearAllSMTPRemove.ItemClick
        Dim view As DevExpress.XtraGrid.Views.Grid.GridView = TryCast(GridControl1.MainView, DevExpress.XtraGrid.Views.Grid.GridView)

        If view IsNot Nothing AndAlso view.RowCount > 0 Then
            If XtraMessageBox.Show("Are you sure you want to remove all SMTP accounts?", "Confirm", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
                view.BeginUpdate()
                For i As Integer = view.RowCount - 1 To 0 Step -1
                    view.DeleteRow(i)
                Next
                view.EndUpdate()
            End If
        Else
            XtraMessageBox.Show("No SMTP accounts to remove.", "Info", MessageBoxButtons.OK, MessageBoxIcon.Information)
        End If
    End Sub





    Private Sub txtLetter_DragEnter(sender As Object, e As Windows.Forms.DragEventArgs) Handles txtLetter.DragEnter

    End Sub

    Private Sub txtLetter_DragDrop(sender As Object, e As Windows.Forms.DragEventArgs) Handles txtLetter.DragDrop

    End Sub
End Class
