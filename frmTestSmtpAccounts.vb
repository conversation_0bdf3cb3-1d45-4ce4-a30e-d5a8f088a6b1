﻿Imports System.ComponentModel
Imports System.Net
Imports System.Net.Mail
Imports DevExpress.XtraEditors
Imports MailKit.Net.Imap
Imports MailKit.Security
Public Class frmTestSmtpAccounts
    Private Sub frmSMTPTester_Details_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        CenterForm(Me, frmIMAP)
        BackgroundWorker1.WorkerSupportsCancellation = True
        If BackgroundWorker1.CancellationPending Then
            BackgroundWorker1.CancelAsync()
        End If
        DevExpress.Data.CurrencyDataController.DisableThreadingProblemsDetection = True
        If BackgroundWorker1.CancellationPending = True Then BackgroundWorker1.CancelAsync()
        If BackgroundWorker1.IsBusy = True Then BackgroundWorker1.CancelAsync()
        BackgroundWorker1.RunWorkerAsync()
    End Sub
    Private Sub BackgroundWorker1_DoWork(sender As Object, e As System.ComponentModel.DoWorkEventArgs) Handles BackgroundWorker1.DoWork
    End Sub
    Private Sub BackgroundWorker1_RunWorkerCompleted(sender As Object, e As RunWorkerCompletedEventArgs) Handles BackgroundWorker1.RunWorkerCompleted
        Me.Dispose()
    End Sub
    Private Sub frmIMAP_Wait_FormClosing(sender As Object, e As FormClosingEventArgs) Handles Me.FormClosing
        If BackgroundWorker1.IsBusy Then
            If BackgroundWorker1.WorkerSupportsCancellation Then
                BackgroundWorker1.CancelAsync()
            End If
        End If
    End Sub
End Class